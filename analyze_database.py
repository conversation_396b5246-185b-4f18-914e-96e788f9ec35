#!/usr/bin/env python3
"""
Database Capacity Analysis for NASS Portal
"""

import sqlite3
import os
import sys

def analyze_database():
    """Analyze the NASS Portal database capacity and usage"""
    
    db_path = 'instance/nass_portal.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get database file size
        db_size = os.path.getsize(db_path)
        
        print("📊 NASS PORTAL DATABASE CAPACITY ANALYSIS")
        print("=" * 60)
        print(f"Database File: {db_path}")
        print(f"Current Size: {db_size:,} bytes")
        print(f"Size in KB: {db_size/1024:.2f} KB")
        print(f"Size in MB: {db_size/1024/1024:.2f} MB")
        print()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print("📋 TABLES AND RECORD COUNTS:")
        print("=" * 60)
        
        total_records = 0
        table_data = []
        
        for table in tables:
            table_name = table[0]
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                total_records += count
                table_data.append((table_name, count))
                print(f"{table_name:<35} {count:>10,} records")
            except Exception as e:
                print(f"{table_name:<35} {'ERROR':>10}")
        
        print("-" * 60)
        print(f"{'TOTAL RECORDS':<35} {total_records:>10,}")
        print()
        
        # Analyze key tables in detail
        key_tables = {
            'students': 'Student Records',
            'courses': 'Available Courses',
            'student_courses': 'Course Registrations',
            'certificates': 'Issued Certificates',
            'student_documents': 'Uploaded Documents',
            'announcements': 'System Announcements',
            'departments': 'Academic Departments',
            'registration_quarters': 'Registration Periods',
            'admin_users': 'Admin Accounts',
            'contact_messages': 'Contact Form Messages'
        }
        
        print("🎯 KEY TABLES DETAILED ANALYSIS:")
        print("=" * 60)
        
        for table_name, description in key_tables.items():
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                
                # Get table schema
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_count = len(columns)
                
                print(f"\n{description} ({table_name}):")
                print(f"  📊 Records: {count:,}")
                print(f"  📋 Columns: {column_count}")
                
                # Show recent activity for some tables
                if table_name in ['students', 'student_courses', 'certificates']:
                    try:
                        if 'created_at' in [col[1] for col in columns]:
                            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE created_at >= date('now', '-30 days')")
                            recent = cursor.fetchone()[0]
                            print(f"  📅 Recent (30 days): {recent:,}")
                    except:
                        pass
                
            except Exception as e:
                print(f"\n{description} ({table_name}): ERROR - {str(e)}")
        
        # Storage capacity analysis
        print("\n💾 STORAGE CAPACITY ANALYSIS:")
        print("=" * 60)
        
        # SQLite theoretical limits
        print("SQLite Theoretical Limits:")
        print(f"  Maximum Database Size: 281 TB")
        print(f"  Maximum Table Size: 268 GB")
        print(f"  Maximum Row Size: 1 GB")
        print(f"  Maximum Columns per Table: 2,000")
        print(f"  Maximum Index Size: 268 GB")
        print()
        
        # Current usage vs practical limits
        practical_limit_mb = 1000  # 1GB practical limit for web hosting
        usage_percentage = (db_size / 1024 / 1024) / practical_limit_mb * 100
        
        print("Practical Usage Analysis:")
        print(f"  Current Size: {db_size/1024/1024:.2f} MB")
        print(f"  Practical Limit: {practical_limit_mb} MB")
        print(f"  Usage: {usage_percentage:.2f}%")
        
        if usage_percentage < 1:
            print("  Status: ✅ Excellent - Very low usage")
        elif usage_percentage < 10:
            print("  Status: ✅ Good - Low usage")
        elif usage_percentage < 50:
            print("  Status: ⚠️  Moderate - Monitor growth")
        else:
            print("  Status: ❌ High - Consider optimization")
        
        # Estimate capacity for growth
        print("\n📈 GROWTH CAPACITY ESTIMATES:")
        print("=" * 60)
        
        # Estimate record sizes
        avg_student_size = 2048  # bytes per student record (with documents)
        avg_course_reg_size = 512  # bytes per course registration
        avg_document_size = 1024 * 1024  # 1MB average document size
        
        current_students = next((count for name, count in table_data if name == 'students'), 0)
        current_registrations = next((count for name, count in table_data if name == 'student_courses'), 0)
        current_documents = next((count for name, count in table_data if name == 'student_documents'), 0)
        
        print(f"Current Capacity:")
        print(f"  Students: {current_students:,}")
        print(f"  Course Registrations: {current_registrations:,}")
        print(f"  Documents: {current_documents:,}")
        print()
        
        # Estimate future capacity
        remaining_mb = practical_limit_mb - (db_size / 1024 / 1024)
        remaining_bytes = remaining_mb * 1024 * 1024
        
        estimated_additional_students = int(remaining_bytes * 0.6 / avg_student_size)
        estimated_additional_registrations = int(remaining_bytes * 0.3 / avg_course_reg_size)
        estimated_additional_documents = int(remaining_bytes * 0.1 / avg_document_size)
        
        print(f"Estimated Additional Capacity:")
        print(f"  Additional Students: ~{estimated_additional_students:,}")
        print(f"  Additional Registrations: ~{estimated_additional_registrations:,}")
        print(f"  Additional Documents: ~{estimated_additional_documents:,}")
        print()
        
        # Performance recommendations
        print("🚀 PERFORMANCE RECOMMENDATIONS:")
        print("=" * 60)
        
        if db_size < 10 * 1024 * 1024:  # Less than 10MB
            print("✅ Database size is optimal for performance")
        elif db_size < 100 * 1024 * 1024:  # Less than 100MB
            print("✅ Database size is good, consider indexing optimization")
        else:
            print("⚠️  Consider database optimization strategies")
        
        print("\nRecommended Actions:")
        print("1. Regular database maintenance (VACUUM)")
        print("2. Implement data archiving for old records")
        print("3. Optimize file storage (move large files to external storage)")
        print("4. Monitor query performance")
        print("5. Consider database partitioning for large datasets")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error analyzing database: {str(e)}")

if __name__ == "__main__":
    analyze_database()
