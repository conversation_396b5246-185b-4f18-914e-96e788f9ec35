import sqlite3
import os

# Database file info
db_path = 'instance/nass_portal.db'
db_size = os.path.getsize(db_path)

print('📊 NASS PORTAL DATABASE ANALYSIS')
print('=' * 50)
print(f'File: {db_path}')
print(f'Size: {db_size:,} bytes ({db_size/1024:.1f} KB)')
print(f'Size: {db_size/1024/1024:.2f} MB')
print()

# Connect and get basic info
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Get all tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()

print(f'📋 TABLES ({len(tables)} total):')
print('=' * 50)

total_records = 0
for table in tables:
    table_name = table[0]
    try:
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        total_records += count
        print(f'{table_name:<30} {count:>8,} records')
    except:
        print(f'{table_name:<30} {"ERROR":>8}')

print('-' * 50)
print(f'{"TOTAL RECORDS":<30} {total_records:>8,}')

# Key tables analysis
key_tables = ['students', 'courses', 'student_courses', 'certificates']
print(f'\n🎯 KEY TABLES:')
print('=' * 30)

for table in key_tables:
    try:
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f'{table}: {count:,}')
    except:
        print(f'{table}: Not found')

# Capacity analysis
print(f'\n💾 CAPACITY ANALYSIS:')
print('=' * 30)
print(f'Current size: {db_size/1024/1024:.2f} MB')
print(f'SQLite max size: 281 TB')
print(f'Practical limit: 1 GB')
usage_pct = (db_size/1024/1024) / 1000 * 100
print(f'Usage: {usage_pct:.3f}%')

if usage_pct < 1:
    print('Status: ✅ Excellent')
elif usage_pct < 10:
    print('Status: ✅ Good')
else:
    print('Status: ⚠️ Monitor')

conn.close()
