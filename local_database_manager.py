"""
Local Database Manager for Unlimited Capacity
Implements database sharding, partitioning, and optimization for local servers
"""

import sqlite3
import hashlib
import os
import json
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional
from contextlib import contextmanager

class LocalDatabaseManager:
    """Manages unlimited database capacity locally"""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.shard_count = self.config.get('shard_count', 100)
        self.shard_dir = Path(self.config.get('shard_directory', 'instance/shards'))
        self.shard_dir.mkdir(parents=True, exist_ok=True)
        
        # Connection pools for each shard
        self.connection_pools = {}
        self.pool_lock = threading.Lock()
        
        # Initialize shards
        self.initialize_shards()
    
    def initialize_shards(self):
        """Initialize all database shards"""
        print(f"🔀 Initializing {self.shard_count} database shards...")
        
        for i in range(self.shard_count):
            shard_path = self.shard_dir / f"shard_{i}.db"
            self.create_shard_schema(shard_path)
        
        print(f"✅ {self.shard_count} shards initialized")
        print(f"📊 Total capacity: {self.shard_count * 281} TB")
    
    def create_shard_schema(self, shard_path: Path):
        """Create schema for a database shard"""
        with sqlite3.connect(str(shard_path)) as conn:
            # Optimize SQLite settings for performance
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=-64000")  # 64MB cache
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")  # 256MB
            
            # Create tables
            conn.executescript("""
                CREATE TABLE IF NOT EXISTS students (
                    id INTEGER PRIMARY KEY,
                    army_number TEXT UNIQUE,
                    email TEXT UNIQUE,
                    first_name TEXT,
                    last_name TEXT,
                    rank TEXT,
                    corps TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE TABLE IF NOT EXISTS courses (
                    id INTEGER PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    category TEXT,
                    level TEXT,
                    duration INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE TABLE IF NOT EXISTS student_courses (
                    id INTEGER PRIMARY KEY,
                    student_id INTEGER,
                    course_id INTEGER,
                    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'registered',
                    grade TEXT,
                    completion_date TIMESTAMP
                );
                
                CREATE TABLE IF NOT EXISTS certificates (
                    id INTEGER PRIMARY KEY,
                    student_id INTEGER,
                    course_id INTEGER,
                    certificate_number TEXT UNIQUE,
                    issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    file_path TEXT
                );
                
                -- Create indexes for performance
                CREATE INDEX IF NOT EXISTS idx_students_army_number ON students(army_number);
                CREATE INDEX IF NOT EXISTS idx_students_email ON students(email);
                CREATE INDEX IF NOT EXISTS idx_student_courses_student_id ON student_courses(student_id);
                CREATE INDEX IF NOT EXISTS idx_student_courses_course_id ON student_courses(course_id);
                CREATE INDEX IF NOT EXISTS idx_certificates_student_id ON certificates(student_id);
            """)
    
    def get_shard_id(self, key: str) -> int:
        """Get shard ID for a given key using consistent hashing"""
        hash_value = int(hashlib.md5(str(key).encode()).hexdigest(), 16)
        return hash_value % self.shard_count
    
    @contextmanager
    def get_connection(self, shard_id: int):
        """Get database connection for a specific shard"""
        shard_path = self.shard_dir / f"shard_{shard_id}.db"
        
        with self.pool_lock:
            if shard_id not in self.connection_pools:
                self.connection_pools[shard_id] = []
        
        # Try to get existing connection from pool
        conn = None
        with self.pool_lock:
            if self.connection_pools[shard_id]:
                conn = self.connection_pools[shard_id].pop()
        
        # Create new connection if none available
        if conn is None:
            conn = sqlite3.connect(str(shard_path))
            conn.row_factory = sqlite3.Row
        
        try:
            yield conn
        finally:
            # Return connection to pool
            with self.pool_lock:
                if len(self.connection_pools[shard_id]) < 10:  # Max 10 connections per shard
                    self.connection_pools[shard_id].append(conn)
                else:
                    conn.close()
    
    def insert_student(self, student_data: Dict[str, Any]) -> int:
        """Insert student into appropriate shard"""
        shard_id = self.get_shard_id(student_data.get('army_number', ''))
        
        with self.get_connection(shard_id) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO students (army_number, email, first_name, last_name, rank, corps)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                student_data.get('army_number'),
                student_data.get('email'),
                student_data.get('first_name'),
                student_data.get('last_name'),
                student_data.get('rank'),
                student_data.get('corps')
            ))
            conn.commit()
            return cursor.lastrowid
    
    def get_student(self, army_number: str) -> Optional[Dict[str, Any]]:
        """Get student from appropriate shard"""
        shard_id = self.get_shard_id(army_number)
        
        with self.get_connection(shard_id) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM students WHERE army_number = ?", (army_number,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_all_students(self, limit: int = None, offset: int = 0) -> List[Dict[str, Any]]:
        """Get students from all shards"""
        all_students = []
        
        for shard_id in range(self.shard_count):
            with self.get_connection(shard_id) as conn:
                cursor = conn.cursor()
                query = "SELECT * FROM students ORDER BY created_at DESC"
                if limit:
                    query += f" LIMIT {limit} OFFSET {offset}"
                
                cursor.execute(query)
                rows = cursor.fetchall()
                all_students.extend([dict(row) for row in rows])
        
        # Sort by created_at and apply global limit/offset
        all_students.sort(key=lambda x: x['created_at'], reverse=True)
        
        if limit:
            start = offset
            end = offset + limit
            return all_students[start:end]
        
        return all_students
    
    def register_student_for_course(self, student_id: int, course_id: int, army_number: str) -> int:
        """Register student for course in appropriate shard"""
        shard_id = self.get_shard_id(army_number)
        
        with self.get_connection(shard_id) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO student_courses (student_id, course_id)
                VALUES (?, ?)
            """, (student_id, course_id))
            conn.commit()
            return cursor.lastrowid
    
    def get_student_courses(self, army_number: str) -> List[Dict[str, Any]]:
        """Get all courses for a student"""
        shard_id = self.get_shard_id(army_number)
        
        with self.get_connection(shard_id) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT sc.*, c.title, c.description, c.category, c.level
                FROM student_courses sc
                JOIN students s ON sc.student_id = s.id
                JOIN courses c ON sc.course_id = c.id
                WHERE s.army_number = ?
                ORDER BY sc.registration_date DESC
            """, (army_number,))
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    
    def vacuum_all_shards(self):
        """Vacuum all database shards to optimize storage"""
        print("🧹 Vacuuming all database shards...")
        
        for shard_id in range(self.shard_count):
            shard_path = self.shard_dir / f"shard_{shard_id}.db"
            if shard_path.exists():
                with sqlite3.connect(str(shard_path)) as conn:
                    conn.execute("VACUUM")
                    conn.execute("ANALYZE")
        
        print("✅ All shards vacuumed and optimized")
    
    def get_shard_statistics(self) -> Dict[str, Any]:
        """Get statistics for all shards"""
        stats = {
            'total_shards': self.shard_count,
            'shard_stats': [],
            'total_records': 0,
            'total_size_mb': 0
        }
        
        for shard_id in range(self.shard_count):
            shard_path = self.shard_dir / f"shard_{shard_id}.db"
            if shard_path.exists():
                size_mb = shard_path.stat().st_size / (1024 * 1024)
                
                with self.get_connection(shard_id) as conn:
                    cursor = conn.cursor()
                    
                    # Count records in each table
                    cursor.execute("SELECT COUNT(*) FROM students")
                    student_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM courses")
                    course_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM student_courses")
                    registration_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM certificates")
                    certificate_count = cursor.fetchone()[0]
                    
                    total_records = student_count + course_count + registration_count + certificate_count
                    
                    shard_stat = {
                        'shard_id': shard_id,
                        'size_mb': round(size_mb, 2),
                        'students': student_count,
                        'courses': course_count,
                        'registrations': registration_count,
                        'certificates': certificate_count,
                        'total_records': total_records
                    }
                    
                    stats['shard_stats'].append(shard_stat)
                    stats['total_records'] += total_records
                    stats['total_size_mb'] += size_mb
        
        stats['total_size_mb'] = round(stats['total_size_mb'], 2)
        stats['average_size_mb'] = round(stats['total_size_mb'] / self.shard_count, 2)
        stats['capacity_utilization'] = f"{(stats['total_size_mb'] / (self.shard_count * 281 * 1024)):.6f}%"
        
        return stats
    
    def backup_all_shards(self, backup_dir: Path):
        """Backup all database shards"""
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"💾 Backing up {self.shard_count} shards to {backup_dir}...")
        
        for shard_id in range(self.shard_count):
            shard_path = self.shard_dir / f"shard_{shard_id}.db"
            if shard_path.exists():
                backup_path = backup_dir / f"shard_{shard_id}_backup.db"
                
                # Use SQLite backup API for consistent backup
                with sqlite3.connect(str(shard_path)) as source:
                    with sqlite3.connect(str(backup_path)) as backup:
                        source.backup(backup)
        
        print("✅ All shards backed up successfully")

# Example usage and testing
def test_local_database_manager():
    """Test the local database manager"""
    config = {
        'shard_count': 10,  # Smaller number for testing
        'shard_directory': 'test_shards'
    }
    
    db_manager = LocalDatabaseManager(config)
    
    # Test student insertion
    student_data = {
        'army_number': 'N/12345',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'Student',
        'rank': 'Lieutenant',
        'corps': 'Signals'
    }
    
    student_id = db_manager.insert_student(student_data)
    print(f"Inserted student with ID: {student_id}")
    
    # Test student retrieval
    retrieved_student = db_manager.get_student('N/12345')
    print(f"Retrieved student: {retrieved_student}")
    
    # Get statistics
    stats = db_manager.get_shard_statistics()
    print(f"Database statistics: {json.dumps(stats, indent=2)}")

if __name__ == "__main__":
    test_local_database_manager()
