"""
Local Storage Manager for Unlimited Capacity
Implements multi-drive storage, compression, and tiering for local servers
"""

import os
import shutil
import hashlib
import gzip
import zipfile
from pathlib import Path
from typing import Dict, List, Any, Optional
from PIL import Image
import threading
from datetime import datetime, timedelta

class LocalStorageManager:
    """Manages unlimited file storage capacity locally"""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.storage_drives = self.config.get('storage_drives', {})
        self.compression_config = self.config.get('compression_config', {})
        
        # Initialize storage directories
        self.initialize_storage_drives()
        
        # File access tracking for tiering
        self.access_tracker = {}
        self.tracker_lock = threading.Lock()
    
    def initialize_storage_drives(self):
        """Initialize all storage drives and directories"""
        print("💾 Initializing multi-drive storage system...")
        
        for drive_name, drive_config in self.storage_drives.items():
            drive_path = Path(drive_config['path'])
            drive_path.mkdir(parents=True, exist_ok=True)
            
            # Create subdirectories for different file types
            for file_type in drive_config.get('files', []):
                (drive_path / file_type).mkdir(parents=True, exist_ok=True)
            
            print(f"  ✅ {drive_name}: {drive_path} ({drive_config['type']})")
        
        print("✅ Multi-drive storage system initialized")
    
    def get_optimal_storage_location(self, file_type: str, file_size: int) -> Path:
        """Determine optimal storage location based on file type and access pattern"""
        
        # Hot storage for frequently accessed files
        if file_type in ['passport_photos', 'certificates', 'active_documents']:
            if 'hot_storage' in self.storage_drives:
                return Path(self.storage_drives['hot_storage']['path']) / file_type
        
        # Warm storage for occasionally accessed files
        elif file_type in ['course_materials', 'completed_documents']:
            if 'warm_storage' in self.storage_drives:
                return Path(self.storage_drives['warm_storage']['path']) / file_type
        
        # Cold storage for rarely accessed files
        elif file_type in ['archived_documents', 'old_backups']:
            if 'cold_storage' in self.storage_drives:
                return Path(self.storage_drives['cold_storage']['path']) / file_type
        
        # Default to hot storage
        return Path(self.storage_drives.get('hot_storage', {}).get('path', 'storage')) / file_type
    
    def compress_file(self, file_path: Path, file_type: str) -> Path:
        """Compress file based on type and configuration"""
        
        if file_type not in self.compression_config:
            return file_path
        
        config = self.compression_config[file_type]
        
        if file_type == 'images':
            return self.compress_image(file_path, config)
        elif file_type == 'documents':
            return self.compress_document(file_path, config)
        else:
            return self.compress_generic(file_path, config)
    
    def compress_image(self, file_path: Path, config: Dict) -> Path:
        """Compress image files using WebP or optimized JPEG"""
        try:
            with Image.open(file_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Compress to WebP
                compressed_path = file_path.with_suffix('.webp')
                img.save(compressed_path, 'WebP', quality=config.get('quality', 85), optimize=True)
                
                # Check if compression was beneficial
                if compressed_path.stat().st_size < file_path.stat().st_size:
                    file_path.unlink()  # Remove original
                    return compressed_path
                else:
                    compressed_path.unlink()  # Remove compressed version
                    return file_path
        
        except Exception as e:
            print(f"Image compression failed for {file_path}: {e}")
            return file_path
    
    def compress_document(self, file_path: Path, config: Dict) -> Path:
        """Compress document files using ZIP"""
        try:
            compressed_path = file_path.with_suffix(file_path.suffix + '.zip')
            
            with zipfile.ZipFile(compressed_path, 'w', zipfile.ZIP_DEFLATED, 
                               compresslevel=config.get('compression_level', 9)) as zf:
                zf.write(file_path, file_path.name)
            
            # Check if compression was beneficial
            if compressed_path.stat().st_size < file_path.stat().st_size * 0.9:  # At least 10% reduction
                file_path.unlink()  # Remove original
                return compressed_path
            else:
                compressed_path.unlink()  # Remove compressed version
                return file_path
        
        except Exception as e:
            print(f"Document compression failed for {file_path}: {e}")
            return file_path
    
    def compress_generic(self, file_path: Path, config: Dict) -> Path:
        """Compress generic files using GZIP"""
        try:
            compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb', compresslevel=config.get('compression_level', 9)) as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Check if compression was beneficial
            if compressed_path.stat().st_size < file_path.stat().st_size * 0.8:  # At least 20% reduction
                file_path.unlink()  # Remove original
                return compressed_path
            else:
                compressed_path.unlink()  # Remove compressed version
                return file_path
        
        except Exception as e:
            print(f"Generic compression failed for {file_path}: {e}")
            return file_path
    
    def store_file(self, file_data: bytes, filename: str, file_type: str, 
                   metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Store file with optimal location and compression"""
        
        # Generate unique filename to avoid conflicts
        file_hash = hashlib.md5(file_data).hexdigest()[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_filename = f"{timestamp}_{file_hash}_{filename}"
        
        # Get optimal storage location
        storage_path = self.get_optimal_storage_location(file_type, len(file_data))
        storage_path.mkdir(parents=True, exist_ok=True)
        
        # Write file
        file_path = storage_path / unique_filename
        with open(file_path, 'wb') as f:
            f.write(file_data)
        
        # Compress if configured
        compressed_path = self.compress_file(file_path, file_type)
        
        # Track file access
        with self.tracker_lock:
            self.access_tracker[str(compressed_path)] = {
                'created_at': datetime.now(),
                'last_accessed': datetime.now(),
                'access_count': 0,
                'file_type': file_type,
                'original_size': len(file_data),
                'compressed_size': compressed_path.stat().st_size,
                'metadata': metadata or {}
            }
        
        return {
            'file_path': str(compressed_path),
            'original_filename': filename,
            'stored_filename': compressed_path.name,
            'file_type': file_type,
            'size': compressed_path.stat().st_size,
            'compression_ratio': compressed_path.stat().st_size / len(file_data),
            'storage_location': str(storage_path)
        }
    
    def retrieve_file(self, file_path: str) -> Optional[bytes]:
        """Retrieve file and decompress if necessary"""
        path = Path(file_path)
        
        if not path.exists():
            return None
        
        # Update access tracking
        with self.tracker_lock:
            if file_path in self.access_tracker:
                self.access_tracker[file_path]['last_accessed'] = datetime.now()
                self.access_tracker[file_path]['access_count'] += 1
        
        # Read and decompress file
        try:
            if path.suffix == '.gz':
                with gzip.open(path, 'rb') as f:
                    return f.read()
            elif path.suffix == '.zip':
                with zipfile.ZipFile(path, 'r') as zf:
                    # Get the first file in the zip
                    names = zf.namelist()
                    if names:
                        return zf.read(names[0])
            else:
                with open(path, 'rb') as f:
                    return f.read()
        
        except Exception as e:
            print(f"File retrieval failed for {file_path}: {e}")
            return None
    
    def move_file_to_tier(self, file_path: str, target_tier: str):
        """Move file to different storage tier based on access pattern"""
        source_path = Path(file_path)
        
        if not source_path.exists():
            return False
        
        # Determine target storage location
        file_type = self.access_tracker.get(file_path, {}).get('file_type', 'documents')
        
        if target_tier == 'hot':
            target_base = Path(self.storage_drives['hot_storage']['path'])
        elif target_tier == 'warm':
            target_base = Path(self.storage_drives['warm_storage']['path'])
        elif target_tier == 'cold':
            target_base = Path(self.storage_drives['cold_storage']['path'])
        else:
            return False
        
        target_path = target_base / file_type / source_path.name
        target_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Move file
        try:
            shutil.move(str(source_path), str(target_path))
            
            # Update tracking
            with self.tracker_lock:
                if file_path in self.access_tracker:
                    self.access_tracker[str(target_path)] = self.access_tracker.pop(file_path)
            
            return True
        
        except Exception as e:
            print(f"File tier migration failed: {e}")
            return False
    
    def optimize_storage_tiers(self):
        """Automatically optimize storage tiers based on access patterns"""
        print("🔄 Optimizing storage tiers based on access patterns...")
        
        now = datetime.now()
        moved_files = 0
        
        with self.tracker_lock:
            for file_path, info in list(self.access_tracker.items()):
                days_since_access = (now - info['last_accessed']).days
                access_frequency = info['access_count'] / max(1, (now - info['created_at']).days)
                
                current_tier = self.get_current_tier(file_path)
                target_tier = None
                
                # Determine target tier
                if access_frequency > 1 or days_since_access < 7:  # Hot data
                    target_tier = 'hot'
                elif access_frequency > 0.1 or days_since_access < 30:  # Warm data
                    target_tier = 'warm'
                else:  # Cold data
                    target_tier = 'cold'
                
                # Move if necessary
                if current_tier != target_tier:
                    if self.move_file_to_tier(file_path, target_tier):
                        moved_files += 1
        
        print(f"✅ Storage optimization complete. Moved {moved_files} files.")
    
    def get_current_tier(self, file_path: str) -> str:
        """Determine current storage tier of a file"""
        path = Path(file_path)
        
        for drive_name, drive_config in self.storage_drives.items():
            if str(path).startswith(drive_config['path']):
                if 'hot' in drive_name:
                    return 'hot'
                elif 'warm' in drive_name:
                    return 'warm'
                elif 'cold' in drive_name:
                    return 'cold'
        
        return 'unknown'
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """Get comprehensive storage statistics"""
        stats = {
            'drives': {},
            'total_files': 0,
            'total_size_gb': 0,
            'compression_savings_gb': 0,
            'tier_distribution': {'hot': 0, 'warm': 0, 'cold': 0}
        }
        
        for drive_name, drive_config in self.storage_drives.items():
            drive_path = Path(drive_config['path'])
            
            if drive_path.exists():
                drive_stats = self.get_directory_stats(drive_path)
                stats['drives'][drive_name] = {
                    'path': str(drive_path),
                    'type': drive_config['type'],
                    'files': drive_stats['files'],
                    'size_gb': drive_stats['size_gb'],
                    'available_space_gb': self.get_available_space(drive_path)
                }
                
                stats['total_files'] += drive_stats['files']
                stats['total_size_gb'] += drive_stats['size_gb']
        
        # Calculate compression savings
        with self.tracker_lock:
            for info in self.access_tracker.values():
                original_size = info.get('original_size', 0)
                compressed_size = info.get('compressed_size', 0)
                if original_size > compressed_size:
                    stats['compression_savings_gb'] += (original_size - compressed_size) / (1024**3)
        
        stats['total_size_gb'] = round(stats['total_size_gb'], 2)
        stats['compression_savings_gb'] = round(stats['compression_savings_gb'], 2)
        
        return stats
    
    def get_directory_stats(self, directory: Path) -> Dict[str, Any]:
        """Get statistics for a directory"""
        total_size = 0
        file_count = 0
        
        try:
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1
        except PermissionError:
            pass
        
        return {
            'files': file_count,
            'size_gb': round(total_size / (1024**3), 2)
        }
    
    def get_available_space(self, path: Path) -> float:
        """Get available disk space in GB"""
        try:
            stat = shutil.disk_usage(path)
            return round(stat.free / (1024**3), 2)
        except:
            return 0.0
    
    def cleanup_old_files(self, days_old: int = 365):
        """Clean up files older than specified days"""
        print(f"🧹 Cleaning up files older than {days_old} days...")
        
        cutoff_date = datetime.now() - timedelta(days=days_old)
        cleaned_files = 0
        
        with self.tracker_lock:
            for file_path, info in list(self.access_tracker.items()):
                if info['created_at'] < cutoff_date and info['access_count'] == 0:
                    try:
                        Path(file_path).unlink()
                        del self.access_tracker[file_path]
                        cleaned_files += 1
                    except:
                        pass
        
        print(f"✅ Cleaned up {cleaned_files} old files")

# Example usage
def test_local_storage_manager():
    """Test the local storage manager"""
    config = {
        'storage_drives': {
            'hot_storage': {'path': 'test_storage/hot', 'type': 'SSD', 'files': ['passport_photos']},
            'warm_storage': {'path': 'test_storage/warm', 'type': 'HDD', 'files': ['documents']},
            'cold_storage': {'path': 'test_storage/cold', 'type': 'HDD', 'files': ['archives']}
        },
        'compression_config': {
            'images': {'quality': 85},
            'documents': {'compression_level': 9}
        }
    }
    
    storage_manager = LocalStorageManager(config)
    
    # Test file storage
    test_data = b"This is test file content for the NASS Portal storage system."
    result = storage_manager.store_file(test_data, "test_document.txt", "documents")
    print(f"Stored file: {result}")
    
    # Test file retrieval
    retrieved_data = storage_manager.retrieve_file(result['file_path'])
    print(f"Retrieved data matches: {retrieved_data == test_data}")
    
    # Get statistics
    stats = storage_manager.get_storage_statistics()
    print(f"Storage statistics: {stats}")

if __name__ == "__main__":
    test_local_storage_manager()
