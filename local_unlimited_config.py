"""
Local Unlimited Capacity Configuration for NASS Portal
Offline/Local server configuration for unlimited capacity
"""

import os
import multiprocessing
from pathlib import Path

class LocalUnlimitedConfig:
    """Configuration for unlimited capacity on local servers"""
    
    # Base paths
    BASE_DIR = Path(__file__).parent
    DATA_DIR = BASE_DIR / "unlimited_data"
    BACKUP_DIR = BASE_DIR / "backups"
    CACHE_DIR = BASE_DIR / "cache"
    
    # System resources
    CPU_CORES = multiprocessing.cpu_count()
    MEMORY_GB = 8  # Adjust based on available RAM
    
    # Database Sharding Configuration
    DATABASE_SHARDING = {
        'enabled': True,
        'shard_count': 100,  # Can expand to 1000+
        'shard_key': 'id',
        'shard_directory': DATA_DIR / "shards",
        'databases': {
            'students': {
                'shards': [f'students_shard_{i}.db' for i in range(100)],
                'routing': 'hash(student_id) % shard_count'
            },
            'courses': {
                'shards': [f'courses_shard_{i}.db' for i in range(100)],
                'routing': 'hash(course_id) % shard_count'
            },
            'registrations': {
                'shards': [f'registrations_shard_{i}.db' for i in range(100)],
                'routing': 'hash(student_id) % shard_count'
            }
        },
        'capacity_per_shard': '281 TB',
        'total_capacity': '28,100 TB'
    }
    
    # Multiple Database Engines
    MULTIPLE_DATABASES = {
        'main': {
            'path': 'instance/main.db',
            'purpose': 'Core application data',
            'engine': 'sqlite',
            'max_size': '281 TB'
        },
        'analytics': {
            'path': 'instance/analytics.db',
            'purpose': 'Analytics and reporting',
            'engine': 'sqlite',
            'max_size': '281 TB'
        },
        'logs': {
            'path': 'instance/logs.db',
            'purpose': 'Application logs',
            'engine': 'sqlite',
            'max_size': '281 TB'
        },
        'cache': {
            'path': 'instance/cache.db',
            'purpose': 'Database cache',
            'engine': 'sqlite',
            'max_size': '281 TB'
        },
        'sessions': {
            'path': 'instance/sessions.db',
            'purpose': 'User sessions',
            'engine': 'sqlite',
            'max_size': '281 TB'
        }
    }
    
    # Multi-Drive Storage Configuration
    STORAGE_DRIVES = {
        'hot_storage': {
            'path': 'C:/nass_hot_storage',
            'type': 'SSD',
            'purpose': 'Frequently accessed files',
            'files': ['passport_photos', 'certificates', 'active_documents']
        },
        'warm_storage': {
            'path': 'D:/nass_warm_storage', 
            'type': 'HDD',
            'purpose': 'Occasionally accessed files',
            'files': ['course_materials', 'completed_documents']
        },
        'cold_storage': {
            'path': 'E:/nass_cold_storage',
            'type': 'HDD',
            'purpose': 'Rarely accessed files',
            'files': ['archived_documents', 'old_backups']
        },
        'network_storage': {
            'path': '//server/nass_network_storage',
            'type': 'Network',
            'purpose': 'Backup and archive',
            'files': ['backups', 'disaster_recovery']
        }
    }
    
    # File Compression Configuration
    COMPRESSION_CONFIG = {
        'images': {
            'algorithm': 'WebP',
            'quality': 85,
            'compression_ratio': 0.7,  # 70% size reduction
            'extensions': ['.jpg', '.jpeg', '.png']
        },
        'documents': {
            'algorithm': 'ZIP',
            'compression_level': 9,
            'compression_ratio': 0.6,  # 60% size reduction
            'extensions': ['.pdf', '.doc', '.docx']
        },
        'databases': {
            'algorithm': 'GZIP',
            'compression_level': 9,
            'compression_ratio': 0.8,  # 80% size reduction
            'schedule': 'daily'
        },
        'backups': {
            'algorithm': '7-Zip',
            'compression_level': 'ultra',
            'compression_ratio': 0.9,  # 90% size reduction
            'encryption': True
        }
    }
    
    # Local Caching Configuration
    CACHING_CONFIG = {
        'memory_cache': {
            'type': 'dict',
            'max_size_mb': min(2048, MEMORY_GB * 256),  # 25% of RAM or 2GB max
            'ttl_seconds': 3600,
            'cleanup_interval': 300
        },
        'disk_cache': {
            'type': 'sqlite',
            'path': CACHE_DIR / 'disk_cache.db',
            'max_size_gb': 10,
            'ttl_seconds': 86400,
            'cleanup_interval': 3600
        },
        'file_cache': {
            'type': 'filesystem',
            'path': CACHE_DIR / 'files',
            'max_size_gb': 50,
            'ttl_seconds': 604800,  # 7 days
            'cleanup_interval': 86400
        },
        'query_cache': {
            'type': 'sqlite',
            'path': CACHE_DIR / 'query_cache.db',
            'max_size_gb': 5,
            'ttl_seconds': 1800,  # 30 minutes
            'cleanup_interval': 600
        }
    }
    
    # Multi-Process Configuration
    MULTIPROCESS_CONFIG = {
        'web_workers': {
            'count': CPU_CORES * 2,
            'port_start': 5000,
            'max_requests': 1000,
            'timeout': 30,
            'restart_on_failure': True
        },
        'background_workers': {
            'count': CPU_CORES,
            'queue_path': DATA_DIR / 'task_queue',
            'max_tasks': 100,
            'timeout': 300,
            'restart_on_failure': True
        },
        'database_workers': {
            'count': CPU_CORES,
            'pool_size': CPU_CORES * 4,
            'max_connections': CPU_CORES * 8,
            'timeout': 30
        }
    }
    
    # Load Balancing Configuration
    LOAD_BALANCING = {
        'nginx': {
            'enabled': True,
            'config_path': 'nginx/nginx.conf',
            'upstream_servers': [f'127.0.0.1:{5000 + i}' for i in range(CPU_CORES * 2)],
            'health_check': {
                'path': '/health',
                'interval': 30,
                'timeout': 5
            }
        },
        'round_robin': {
            'enabled': True,
            'sticky_sessions': True,
            'session_timeout': 3600
        }
    }
    
    # Performance Optimization
    PERFORMANCE_CONFIG = {
        'database_optimization': {
            'pragma_settings': {
                'journal_mode': 'WAL',
                'synchronous': 'NORMAL',
                'cache_size': -64000,  # 64MB cache
                'temp_store': 'MEMORY',
                'mmap_size': 268435456  # 256MB
            },
            'connection_pooling': True,
            'prepared_statements': True,
            'batch_operations': True
        },
        'file_optimization': {
            'buffer_size': 65536,  # 64KB
            'async_io': True,
            'memory_mapping': True,
            'compression_on_the_fly': True
        },
        'memory_optimization': {
            'garbage_collection': 'optimized',
            'object_pooling': True,
            'lazy_loading': True,
            'memory_profiling': True
        }
    }
    
    # Backup and Recovery
    BACKUP_CONFIG = {
        'database_backup': {
            'frequency': 'hourly',
            'retention_days': 30,
            'compression': True,
            'encryption': True,
            'incremental': True
        },
        'file_backup': {
            'frequency': 'daily',
            'retention_days': 90,
            'compression': True,
            'encryption': True,
            'differential': True
        },
        'system_backup': {
            'frequency': 'weekly',
            'retention_weeks': 12,
            'full_system': True,
            'compression': True
        }
    }
    
    # Monitoring Configuration
    MONITORING_CONFIG = {
        'system_metrics': {
            'cpu_usage': True,
            'memory_usage': True,
            'disk_usage': True,
            'network_usage': True,
            'process_count': True
        },
        'application_metrics': {
            'request_count': True,
            'response_time': True,
            'error_rate': True,
            'database_queries': True,
            'cache_hit_rate': True
        },
        'alerts': {
            'cpu_threshold': 80,
            'memory_threshold': 85,
            'disk_threshold': 90,
            'response_time_threshold': 2000,
            'error_rate_threshold': 5
        }
    }
    
    # Security Configuration
    SECURITY_CONFIG = {
        'file_permissions': {
            'data_directories': 0o755,
            'database_files': 0o644,
            'log_files': 0o644,
            'backup_files': 0o600
        },
        'encryption': {
            'database_encryption': True,
            'file_encryption': True,
            'backup_encryption': True,
            'algorithm': 'AES-256'
        },
        'access_control': {
            'user_authentication': True,
            'role_based_access': True,
            'session_management': True,
            'audit_logging': True
        }
    }
    
    # Scaling Configuration
    SCALING_CONFIG = {
        'horizontal_scaling': {
            'cluster_nodes': ['server1', 'server2', 'server3'],
            'load_distribution': 'round_robin',
            'data_replication': True,
            'failover': True
        },
        'vertical_scaling': {
            'auto_resource_allocation': True,
            'memory_scaling': True,
            'cpu_scaling': True,
            'storage_scaling': True
        },
        'capacity_planning': {
            'growth_rate': '20% per year',
            'capacity_buffer': '50%',
            'scaling_triggers': {
                'cpu_usage': 70,
                'memory_usage': 75,
                'disk_usage': 80
            }
        }
    }

# Environment-specific configurations
class LocalDevelopmentConfig(LocalUnlimitedConfig):
    DEBUG = True
    TESTING = False
    MULTIPROCESS_CONFIG = {
        **LocalUnlimitedConfig.MULTIPROCESS_CONFIG,
        'web_workers': {'count': 1, 'port_start': 5000}
    }

class LocalProductionConfig(LocalUnlimitedConfig):
    DEBUG = False
    TESTING = False
    # Use full multiprocess configuration

# Configuration mapping
local_config = {
    'development': LocalDevelopmentConfig,
    'production': LocalProductionConfig,
    'default': LocalDevelopmentConfig
}
