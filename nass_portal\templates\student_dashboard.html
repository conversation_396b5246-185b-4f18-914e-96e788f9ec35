{% extends 'base.html' %}

{% block title %}Student Portal - Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/styles.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/modern.css', v='1.1') }}">
<link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
<style>
    :root {
        --primary-color: #3c78c3;
        --secondary-color: #2c5aa0;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --info-color: #17a2b8;
        --danger-color: #dc3545;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --border-radius: 12px;
        --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    body {
        background-color: #f5f7fa;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .dashboard-container {
        min-height: 100vh;
        padding: 20px 0;
    }

    /* Header Section */
    .student-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 40px 30px;
        margin-bottom: 30px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        position: relative;
        overflow: hidden;
    }

    .student-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .student-photo {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        transition: var(--transition);
    }

    .student-photo:hover {
        transform: scale(1.05);
        border-color: rgba(255, 255, 255, 0.6);
    }

    .student-name {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .student-rank {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .student-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .info-item {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        backdrop-filter: blur(10px);
        transition: var(--transition);
    }

    .info-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }

    .info-label {
        font-size: 0.85rem;
        opacity: 0.8;
        margin-bottom: 5px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        font-size: 1.1rem;
        font-weight: 600;
    }

    /* Quick Actions */
    .quick-actions {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 25px;
        height: 100%;
    }

    .quick-actions h5 {
        color: var(--dark-color);
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .action-btn {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 15px 20px;
        margin-bottom: 10px;
        border: none;
        border-radius: 8px;
        background: var(--light-color);
        color: var(--dark-color);
        text-decoration: none;
        transition: var(--transition);
        font-weight: 500;
    }

    .action-btn:hover {
        background: var(--primary-color);
        color: white;
        transform: translateX(5px);
        text-decoration: none;
    }

    .action-btn.danger:hover {
        background: var(--danger-color);
    }

    .action-btn i {
        width: 20px;
        text-align: center;
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 25px;
        box-shadow: var(--box-shadow);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-color);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-card.success::before { background: var(--success-color); }
    .stat-card.warning::before { background: var(--warning-color); }
    .stat-card.info::before { background: var(--info-color); }
    .stat-card.danger::before { background: var(--danger-color); }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 15px;
        background: var(--light-color);
        color: var(--primary-color);
    }

    .stat-card.success .stat-icon { background: rgba(40, 167, 69, 0.1); color: var(--success-color); }
    .stat-card.warning .stat-icon { background: rgba(255, 193, 7, 0.1); color: var(--warning-color); }
    .stat-card.info .stat-icon { background: rgba(23, 162, 184, 0.1); color: var(--info-color); }
    .stat-card.danger .stat-icon { background: rgba(220, 53, 69, 0.1); color: var(--danger-color); }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .stat-change {
        font-size: 0.85rem;
        font-weight: 600;
    }

    .stat-change.positive { color: var(--success-color); }
    .stat-change.negative { color: var(--danger-color); }

    /* Main Content */
    .main-content {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        overflow: hidden;
    }

    .content-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 20px 30px;
        border-bottom: 1px solid #dee2e6;
    }

    .nav-tabs {
        border: none;
        background: transparent;
    }

    .nav-tabs .nav-link {
        border: none;
        background: transparent;
        color: #6c757d;
        font-weight: 600;
        padding: 15px 25px;
        margin-right: 10px;
        border-radius: 8px 8px 0 0;
        transition: var(--transition);
    }

    .nav-tabs .nav-link:hover {
        background: rgba(60, 120, 195, 0.1);
        color: var(--primary-color);
    }

    .nav-tabs .nav-link.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .tab-content {
        padding: 30px;
    }

    /* Course Cards */
    .course-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 25px;
    }

    .course-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        position: relative;
    }

    .course-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-color);
    }

    .course-card-header {
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;
    }

    .course-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-completed {
        background: var(--success-color);
        color: white;
    }

    .status-in-progress {
        background: var(--warning-color);
        color: var(--dark-color);
    }

    .status-registered {
        background: var(--info-color);
        color: white;
    }

    .course-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 10px;
        padding-right: 100px;
    }

    .course-description {
        color: #6c757d;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .course-card-body {
        padding: 20px;
    }

    .course-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .meta-item i {
        color: var(--primary-color);
    }

    .course-progress {
        margin-bottom: 20px;
    }

    .progress {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
    }

    .progress-bar {
        border-radius: 4px;
        transition: width 0.6s ease;
    }

    .course-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn-course {
        flex: 1;
        min-width: 120px;
        padding: 10px 15px;
        border-radius: 8px;
        font-weight: 600;
        transition: var(--transition);
        border: none;
        text-decoration: none;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-course:hover {
        transform: translateY(-2px);
        text-decoration: none;
    }

    .certificate-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: var(--success-color);
        color: white;
        padding: 8px;
        border-radius: 50%;
        font-size: 0.9rem;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    /* Empty States */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }

    .empty-state h4 {
        color: var(--dark-color);
        margin-bottom: 15px;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .course-grid {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 10px 0;
        }

        .student-header {
            padding: 30px 20px;
            margin-bottom: 20px;
        }

        .student-name {
            font-size: 1.8rem;
        }

        .student-info {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .course-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .tab-content {
            padding: 20px;
        }

        .nav-tabs .nav-link {
            padding: 12px 15px;
            margin-right: 5px;
            font-size: 0.9rem;
        }

        .course-actions {
            flex-direction: column;
        }

        .btn-course {
            min-width: auto;
        }
    }

    @media (max-width: 576px) {
        .student-header {
            padding: 20px 15px;
        }

        .student-name {
            font-size: 1.5rem;
        }

        .student-photo {
            width: 100px;
            height: 100px;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .nav-tabs {
            flex-wrap: nowrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .nav-tabs .nav-link {
            white-space: nowrap;
            margin-right: 10px;
        }
    }

    /* Loading Animation */
    .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Notification Badge */
    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }

    /* Pulse Animation */
    .pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    /* Notifications */
    .notifications-container {
        max-height: 600px;
        overflow-y: auto;
    }

    .notification-item {
        display: flex;
        gap: 15px;
        padding: 20px;
        margin-bottom: 15px;
        background: white;
        border-radius: 8px;
        border-left: 4px solid #dee2e6;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: var(--transition);
        position: relative;
    }

    .notification-item:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .notification-item.unread {
        border-left-color: var(--primary-color);
        background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    }

    .notification-item.unread::before {
        content: '';
        position: absolute;
        top: 10px;
        right: 10px;
        width: 8px;
        height: 8px;
        background: var(--primary-color);
        border-radius: 50%;
    }

    .notification-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .notification-content {
        flex: 1;
    }

    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
    }

    .notification-title {
        font-size: 1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
    }

    .notification-time {
        font-size: 0.8rem;
        color: #6c757d;
        white-space: nowrap;
    }

    .notification-message {
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 10px;
    }

    .notification-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .notification-actions .btn {
        font-size: 0.8rem;
        padding: 4px 12px;
    }

    /* Course Materials */
    .materials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .material-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
        transition: var(--transition);
    }

    .material-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    .material-header {
        padding: 15px;
        background: var(--light-color);
        border-bottom: 1px solid #e9ecef;
    }

    .material-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
    }

    .material-course {
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 5px;
    }

    .material-body {
        padding: 15px;
    }

    .material-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .material-type {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .material-size {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .material-actions {
        display: flex;
        gap: 8px;
    }

    .material-actions .btn {
        flex: 1;
        font-size: 0.9rem;
        padding: 8px 12px;
    }

    /* Academic Calendar */
    .calendar-container {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: var(--box-shadow);
    }

    .calendar-header {
        background: var(--primary-color);
        color: white;
        padding: 20px;
        text-align: center;
    }

    .calendar-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .calendar-nav button {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        transition: var(--transition);
    }

    .calendar-nav button:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .calendar-month {
        font-size: 1.5rem;
        font-weight: 600;
    }

    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        background: #e9ecef;
    }

    .calendar-day {
        background: white;
        padding: 10px;
        min-height: 80px;
        position: relative;
        cursor: pointer;
        transition: var(--transition);
    }

    .calendar-day:hover {
        background: var(--light-color);
    }

    .calendar-day.today {
        background: rgba(60, 120, 195, 0.1);
        border: 2px solid var(--primary-color);
    }

    .calendar-day.has-event {
        background: rgba(40, 167, 69, 0.1);
    }

    .calendar-day-number {
        font-weight: 600;
        color: var(--dark-color);
    }

    .calendar-event {
        background: var(--primary-color);
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 0.7rem;
        margin-top: 5px;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .calendar-event.course {
        background: var(--success-color);
    }

    .calendar-event.deadline {
        background: var(--danger-color);
    }

    .calendar-event.exam {
        background: var(--warning-color);
        color: var(--dark-color);
    }

    .calendar-day-header {
        background: var(--primary-color);
        color: white;
        padding: 10px;
        text-align: center;
        font-weight: 600;
        font-size: 0.9rem;
    }

    /* Upcoming Events */
    .upcoming-events {
        max-height: 400px;
        overflow-y: auto;
    }

    .event-item {
        display: flex;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .event-item:last-child {
        border-bottom: none;
    }

    .event-date {
        text-align: center;
        min-width: 50px;
    }

    .event-day {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        line-height: 1;
    }

    .event-month {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .event-details {
        flex: 1;
    }

    .event-title {
        font-size: 1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 5px;
    }

    .event-course {
        font-size: 0.9rem;
        color: var(--primary-color);
        margin-bottom: 3px;
    }

    .event-time {
        font-size: 0.8rem;
        color: #6c757d;
        margin: 0;
    }

    /* Legend */
    .legend-items {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 0.9rem;
    }

    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
    }

    /* Performance Analytics */
    .analytics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .analytics-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: var(--box-shadow);
    }

    .analytics-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
    }

    .analytics-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
    }

    .analytics-chart {
        height: 200px;
        display: flex;
        align-items: end;
        justify-content: space-around;
        border-bottom: 2px solid #e9ecef;
        border-left: 2px solid #e9ecef;
        padding: 10px;
        margin-bottom: 15px;
    }

    .chart-bar {
        background: var(--primary-color);
        width: 30px;
        border-radius: 3px 3px 0 0;
        position: relative;
        transition: var(--transition);
    }

    .chart-bar:hover {
        background: var(--secondary-color);
        transform: scale(1.1);
    }

    .chart-bar::after {
        content: attr(data-value);
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.8rem;
        font-weight: 600;
        color: var(--dark-color);
    }

    .chart-labels {
        display: flex;
        justify-content: space-around;
        font-size: 0.8rem;
        color: #6c757d;
    }

    /* Progress Rings */
    .progress-ring {
        width: 120px;
        height: 120px;
        margin: 0 auto;
        position: relative;
    }

    .progress-ring svg {
        width: 100%;
        height: 100%;
        transform: rotate(-90deg);
    }

    .progress-ring circle {
        fill: none;
        stroke-width: 8;
    }

    .progress-ring .background {
        stroke: #e9ecef;
    }

    .progress-ring .progress {
        stroke: var(--primary-color);
        stroke-linecap: round;
        transition: stroke-dashoffset 0.5s ease;
    }

    .progress-ring .percentage {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 1.2rem;
        font-weight: 700;
        color: var(--dark-color);
    }

    /* Mobile Responsive Updates */
    @media (max-width: 768px) {
        .calendar-grid {
            font-size: 0.8rem;
        }

        .calendar-day {
            min-height: 60px;
            padding: 5px;
        }

        .calendar-event {
            font-size: 0.6rem;
            padding: 1px 3px;
        }

        .materials-grid {
            grid-template-columns: 1fr;
        }

        .analytics-grid {
            grid-template-columns: 1fr;
        }

        .event-item {
            flex-direction: column;
            gap: 10px;
        }

        .event-date {
            align-self: flex-start;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="container-fluid">
        <!-- Student Header -->
        <div class="student-header" data-aos="fade-down">
            <div class="row align-items-center">
                <div class="col-lg-2 col-md-3 text-center mb-3 mb-md-0">
                    {% if student.passport_photo %}
                    <img src="{{ url_for('static', filename='uploads/passport_photos/' + student.passport_photo) }}" class="student-photo" alt="Student Photo">
                    {% else %}
                    <img src="{{ url_for('static', filename='images/placeholder-user.png') }}" class="student-photo" alt="Student Photo">
                    {% endif %}
                </div>
                <div class="col-lg-6 col-md-9 mb-3 mb-lg-0">
                    <h1 class="student-name">{{ student.surname }}, {{ student.other_names }}</h1>
                    <div class="student-rank">{{ student.rank }}</div>
                    <div class="student-info">
                        <div class="info-item">
                            <div class="info-label">Service Number</div>
                            <div class="info-value">{{ student.service_number }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Current Unit</div>
                            <div class="info-value">{{ student.current_unit or 'Not Assigned' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Years in Service</div>
                            <div class="info-value">{{ student.years_in_service or 'N/A' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Corps</div>
                            <div class="info-value">{{ student.corps or 'Not Assigned' }}</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="quick-actions" data-aos="fade-left" data-aos-delay="200">
                        <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                        <a href="{{ url_for('student.profile') }}" class="action-btn">
                            <i class="fas fa-user"></i> View Full Profile
                        </a>
                        <a href="{{ url_for('student.documents') }}" class="action-btn">
                            <i class="fas fa-file-alt"></i> My Documents
                        </a>
                        <a href="{{ url_for('student.profile_edit') }}" class="action-btn">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                        <a href="{{ url_for('student.change_password') }}" class="action-btn">
                            <i class="fas fa-lock"></i> Change Password
                        </a>
                        <a href="{{ url_for('student.logout') }}" class="action-btn danger">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="row mb-4">
                    <div class="col-12">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert" data-aos="fade-up">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        {% endwith %}

        <!-- Statistics Cards -->
        <div class="stats-grid" data-aos="fade-up" data-aos-delay="300">
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stat-number">{{ courses|length }}</div>
                <div class="stat-label">Total Courses</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> Active Enrollments
                </div>
            </div>

            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="stat-number">{{ certificates|length }}</div>
                <div class="stat-label">Certificates Earned</div>
                <div class="stat-change positive">
                    <i class="fas fa-award"></i> Achievements
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number">
                    {% set in_progress = courses|selectattr('status', 'equalto', 'in_progress')|list|length %}
                    {{ in_progress }}
                </div>
                <div class="stat-label">In Progress</div>
                <div class="stat-change">
                    <i class="fas fa-play"></i> Active Learning
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-number">
                    {% if registration_deadline %}
                        <i class="fas fa-calendar-alt"></i>
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="stat-label">Registration Status</div>
                <div class="stat-change">
                    {% if registration_deadline %}
                        <i class="fas fa-check-circle"></i> Open
                    {% else %}
                        <i class="fas fa-times-circle"></i> Closed
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content" data-aos="fade-up" data-aos-delay="400">
            <div class="content-header">
                <ul class="nav nav-tabs" id="studentTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab" aria-controls="courses" aria-selected="true">
                            <i class="fas fa-graduation-cap me-2"></i> My Courses
                            {% if courses %}
                                <span class="badge bg-light text-dark ms-2">{{ courses|length }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="certificates-tab" data-bs-toggle="tab" data-bs-target="#certificates" type="button" role="tab" aria-controls="certificates" aria-selected="false">
                            <i class="fas fa-certificate me-2"></i> Certificates
                            {% if certificates %}
                                <span class="badge bg-light text-dark ms-2">{{ certificates|length }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register" type="button" role="tab" aria-controls="register" aria-selected="false">
                            <i class="fas fa-plus-circle me-2"></i> Register for New Course
                            {% if available_courses %}
                                <span class="badge bg-warning text-dark ms-2">{{ available_courses|length }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab" aria-controls="notifications" aria-selected="false">
                            <i class="fas fa-bell me-2"></i> Notifications
                            <span class="badge bg-danger ms-2" id="notificationCount">3</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button" role="tab" aria-controls="materials" aria-selected="false">
                            <i class="fas fa-book-open me-2"></i> Study Materials
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="calendar-tab" data-bs-toggle="tab" data-bs-target="#calendar" type="button" role="tab" aria-controls="calendar" aria-selected="false">
                            <i class="fas fa-calendar-alt me-2"></i> Academic Calendar
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false">
                            <i class="fas fa-user me-2"></i> Profile Overview
                        </button>
                    </li>
                </ul>
            </div>

            <div class="tab-content" id="studentTabsContent">
                <!-- Courses Tab -->
                <div class="tab-pane fade show active" id="courses" role="tabpanel" aria-labelledby="courses-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">My Course History</h4>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="filterCourses('all')">All</button>
                            <button class="btn btn-outline-success btn-sm" onclick="filterCourses('completed')">Completed</button>
                            <button class="btn btn-outline-warning btn-sm" onclick="filterCourses('in_progress')">In Progress</button>
                            <button class="btn btn-outline-info btn-sm" onclick="filterCourses('registered')">Registered</button>
                        </div>
                    </div>

                    {% if courses %}
                        <div class="course-grid">
                            {% for course in courses %}
                                <div class="course-card" data-status="{{ course.status }}" data-aos="fade-up" data-aos-delay="{{ loop.index * 100 }}">
                                    {% if course.status == 'completed' and course.has_certificate %}
                                        <div class="certificate-badge" title="Certificate Available">
                                            <i class="fas fa-award"></i>
                                        </div>
                                    {% endif %}

                                    <div class="course-card-header">
                                        <span class="course-status status-{{ course.status }}">
                                            {{ course.status|replace('_', ' ')|title }}
                                        </span>
                                        <h5 class="course-title">{{ course.name }}</h5>
                                        <p class="course-description">{{ course.description[:100] }}{% if course.description|length > 100 %}...{% endif %}</p>
                                    </div>

                                    <div class="course-card-body">
                                        <div class="course-meta">
                                            {% if course.start_date and course.end_date %}
                                            <div class="meta-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                <span>{{ course.start_date }} - {{ course.end_date }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.duration %}
                                            <div class="meta-item">
                                                <i class="fas fa-clock"></i>
                                                <span>{{ course.duration }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.level %}
                                            <div class="meta-item">
                                                <i class="fas fa-layer-group"></i>
                                                <span>{{ course.level }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.department %}
                                            <div class="meta-item">
                                                <i class="fas fa-building"></i>
                                                <span>{{ course.department }}</span>
                                            </div>
                                            {% endif %}
                                        </div>

                                        {% if course.status == 'in_progress' %}
                                        <div class="course-progress">
                                            <div class="d-flex justify-content-between mb-1">
                                                <small>Progress</small>
                                                <small>{{ course.progress or 0 }}%</small>
                                            </div>
                                            <div class="progress">
                                                <div class="progress-bar bg-warning" role="progressbar" style="width: {{ course.progress or 0 }}%"></div>
                                            </div>
                                        </div>
                                        {% endif %}

                                        <div class="course-actions">
                                            <a href="{{ url_for('student.course_detail', course_id=course.id) }}" class="btn-course btn btn-outline-primary">
                                                <i class="fas fa-eye"></i> View Details
                                            </a>

                                            {% if course.status == 'completed' and course.has_certificate %}
                                                <a href="{{ url_for('student.certificate', certificate_id=course.certificate_id) }}" class="btn-course btn btn-success">
                                                    <i class="fas fa-download"></i> Certificate
                                                </a>
                                            {% elif course.status == 'in_progress' %}
                                                <a href="#" class="btn-course btn btn-warning">
                                                    <i class="fas fa-play"></i> Continue
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-graduation-cap"></i>
                            <h4>No Courses Yet</h4>
                            <p>You haven't registered for any courses yet. Start your learning journey by registering for a course!</p>
                            {% if available_courses %}
                                <div class="d-flex gap-2 justify-content-center flex-wrap">
                                    <button class="btn btn-primary" onclick="document.getElementById('register-tab').click()">
                                        <i class="fas fa-plus-circle me-2"></i> Browse Available Courses
                                    </button>
                                    <a href="{{ url_for('student.register_course', course_id=available_courses[0].id) }}" class="btn btn-success">
                                        <i class="fas fa-rocket me-2"></i> Quick Register
                                    </a>
                                </div>
                                <p class="text-muted mt-3 mb-0">
                                    <small>{{ available_courses|length }} course{{ 's' if available_courses|length != 1 else '' }} available for registration</small>
                                </p>
                            {% else %}
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i> No courses are currently available for registration. Please check back later.
                                </div>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>

                <!-- Certificates Tab -->
                <div class="tab-pane fade" id="certificates" role="tabpanel" aria-labelledby="certificates-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">My Certificates</h4>
                        <div class="text-muted">
                            <i class="fas fa-award me-1"></i> {{ certificates|length }} Certificate{{ 's' if certificates|length != 1 else '' }}
                        </div>
                    </div>

                    {% if certificates %}
                        <div class="course-grid">
                            {% for cert in certificates %}
                                <div class="course-card" data-aos="fade-up" data-aos-delay="{{ loop.index * 100 }}">
                                    <div class="certificate-badge">
                                        <i class="fas fa-award"></i>
                                    </div>

                                    <div class="course-card-header">
                                        <span class="course-status status-completed">Certified</span>
                                        <h5 class="course-title">{{ cert.course_name }}</h5>
                                        <p class="course-description">Certificate of completion for {{ cert.course_name }}</p>
                                    </div>

                                    <div class="course-card-body">
                                        <div class="course-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-calendar-check"></i>
                                                <span>Issued: {{ cert.issue_date }}</span>
                                            </div>
                                            {% if cert.certificate_number %}
                                            <div class="meta-item">
                                                <i class="fas fa-hashtag"></i>
                                                <span>{{ cert.certificate_number }}</span>
                                            </div>
                                            {% endif %}
                                            {% if cert.grade %}
                                            <div class="meta-item">
                                                <i class="fas fa-star"></i>
                                                <span>Grade: {{ cert.grade }}</span>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <div class="course-actions">
                                            <a href="{{ url_for('student.certificate', certificate_id=cert.id) }}" class="btn-course btn btn-success">
                                                <i class="fas fa-download"></i> Download
                                            </a>
                                            <a href="{{ url_for('student.certificate_view', certificate_id=cert.id) }}" class="btn-course btn btn-outline-primary" target="_blank">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-certificate"></i>
                            <h4>No Certificates Yet</h4>
                            <p>Complete courses to earn certificates. Your achievements will be displayed here.</p>
                            <button class="btn btn-primary" onclick="document.getElementById('courses-tab').click()">
                                <i class="fas fa-graduation-cap me-2"></i> View My Courses
                            </button>
                        </div>
                    {% endif %}
                </div>

                <!-- Register for New Course Tab -->
                <div class="tab-pane fade" id="register" role="tabpanel" aria-labelledby="register-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">Register for a New Course</h4>
                        {% if registration_deadline %}
                        <div class="text-muted">
                            <i class="fas fa-clock me-1"></i> Deadline: {{ registration_deadline }}
                        </div>
                        {% endif %}
                    </div>

                    {% if available_courses %}
                        {% if registration_deadline %}
                        <div class="alert alert-info mb-4" data-aos="fade-up">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle me-3 fs-4"></i>
                                <div>
                                    <strong>Registration Open!</strong><br>
                                    Registration for the current quarter is open until <strong>{{ registration_deadline }}</strong>.
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="course-grid">
                            {% for course in available_courses %}
                                <div class="course-card" data-aos="fade-up" data-aos-delay="{{ loop.index * 100 }}">
                                    <div class="course-card-header">
                                        <span class="course-status" style="background: var(--success-color); color: white;">Available</span>
                                        <h5 class="course-title">{{ course.name }}</h5>
                                        <p class="course-description">{{ course.description[:100] }}{% if course.description|length > 100 %}...{% endif %}</p>
                                    </div>

                                    <div class="course-card-body">
                                        <div class="course-meta">
                                            {% if course.start_date and course.end_date %}
                                            <div class="meta-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                <span>{{ course.start_date }} - {{ course.end_date }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.duration %}
                                            <div class="meta-item">
                                                <i class="fas fa-clock"></i>
                                                <span>{{ course.duration }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.level %}
                                            <div class="meta-item">
                                                <i class="fas fa-layer-group"></i>
                                                <span>{{ course.level }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.max_students %}
                                            <div class="meta-item">
                                                <i class="fas fa-users"></i>
                                                <span>{{ course.available_slots or course.max_students }} slots available</span>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <div class="course-actions">
                                            <a href="{{ url_for('student.register_course', course_id=course.id) }}" class="btn-course btn btn-success">
                                                <i class="fas fa-plus-circle"></i> Register Now
                                            </a>
                                            <a href="#" class="btn-course btn btn-outline-primary" onclick="showCourseDetails('{{ course.id }}')">
                                                <i class="fas fa-info-circle"></i> Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-calendar-times"></i>
                            <h4>No Courses Available</h4>
                            <p>There are no courses available for registration at this time. Please check back later or contact the administration.</p>
                            <a href="{{ url_for('main.contact') }}" class="btn btn-outline-primary">
                                <i class="fas fa-envelope me-2"></i> Contact Administration
                            </a>
                        </div>
                    {% endif %}
                </div>

                <!-- Notifications Tab -->
                <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">Notifications</h4>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                                <i class="fas fa-check-double me-1"></i> Mark All Read
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearNotifications()">
                                <i class="fas fa-trash me-1"></i> Clear All
                            </button>
                        </div>
                    </div>

                    <div class="notifications-container">
                        <!-- Course Registration Reminder -->
                        <div class="notification-item unread" data-type="reminder">
                            <div class="notification-icon bg-warning">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-header">
                                    <h6 class="notification-title">Registration Deadline Approaching</h6>
                                    <span class="notification-time">2 hours ago</span>
                                </div>
                                <p class="notification-message">
                                    Registration for the First Quarter 2024 closes in 5 days. Don't miss out on available courses!
                                </p>
                                <div class="notification-actions">
                                    <button class="btn btn-sm btn-warning" onclick="document.getElementById('register-tab').click()">
                                        <i class="fas fa-graduation-cap me-1"></i> View Courses
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Course Update -->
                        <div class="notification-item unread" data-type="course">
                            <div class="notification-icon bg-info">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-header">
                                    <h6 class="notification-title">New Study Materials Available</h6>
                                    <span class="notification-time">1 day ago</span>
                                </div>
                                <p class="notification-message">
                                    New study materials have been uploaded for "Advanced Communication Systems". Check your course materials section.
                                </p>
                                <div class="notification-actions">
                                    <button class="btn btn-sm btn-info" onclick="viewCourseMaterials(1)">
                                        <i class="fas fa-download me-1"></i> Download Materials
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- System Update -->
                        <div class="notification-item read" data-type="system">
                            <div class="notification-icon bg-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-header">
                                    <h6 class="notification-title">Profile Updated Successfully</h6>
                                    <span class="notification-time">3 days ago</span>
                                </div>
                                <p class="notification-message">
                                    Your profile information has been updated successfully. All changes are now active.
                                </p>
                            </div>
                        </div>

                        <!-- Certificate Available -->
                        <div class="notification-item read" data-type="certificate">
                            <div class="notification-icon bg-success">
                                <i class="fas fa-award"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-header">
                                    <h6 class="notification-title">Certificate Ready for Download</h6>
                                    <span class="notification-time">1 week ago</span>
                                </div>
                                <p class="notification-message">
                                    Your certificate for "Basic Signals Course" is now ready for download. Congratulations on completing the course!
                                </p>
                                <div class="notification-actions">
                                    <button class="btn btn-sm btn-success" onclick="document.getElementById('certificates-tab').click()">
                                        <i class="fas fa-download me-1"></i> Download Certificate
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Course Reminder -->
                        <div class="notification-item read" data-type="reminder">
                            <div class="notification-icon bg-primary">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-header">
                                    <h6 class="notification-title">Course Starting Soon</h6>
                                    <span class="notification-time">2 weeks ago</span>
                                </div>
                                <p class="notification-message">
                                    Your course "Digital Signal Processing" starts next Monday. Make sure you have all required materials ready.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State for No Notifications -->
                    <div class="empty-state d-none" id="noNotifications">
                        <i class="fas fa-bell-slash"></i>
                        <h4>No Notifications</h4>
                        <p>You're all caught up! No new notifications at this time.</p>
                    </div>
                </div>

                <!-- Study Materials Tab -->
                <div class="tab-pane fade" id="materials" role="tabpanel" aria-labelledby="materials-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">Study Materials</h4>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" style="width: auto;" onchange="filterMaterials(this.value)">
                                <option value="all">All Courses</option>
                                <option value="current">Current Courses</option>
                                <option value="completed">Completed Courses</option>
                            </select>
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshMaterials()">
                                <i class="fas fa-sync-alt me-1"></i> Refresh
                            </button>
                        </div>
                    </div>

                    <div class="materials-grid">
                        <!-- Lecture Notes -->
                        <div class="material-card" data-course="current">
                            <div class="material-header">
                                <h6 class="material-title">Communication Systems - Lecture Notes</h6>
                                <div class="material-course">Advanced Communication Systems</div>
                            </div>
                            <div class="material-body">
                                <div class="material-meta">
                                    <div class="material-type">
                                        <i class="fas fa-file-pdf text-danger"></i>
                                        <span>PDF Document</span>
                                    </div>
                                    <div class="material-size">2.5 MB</div>
                                </div>
                                <p class="text-muted small mb-3">Comprehensive lecture notes covering digital communication protocols and signal processing techniques.</p>
                                <div class="material-actions">
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-download me-1"></i> Download
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-eye me-1"></i> Preview
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Lab Manual -->
                        <div class="material-card" data-course="current">
                            <div class="material-header">
                                <h6 class="material-title">DSP Lab Manual</h6>
                                <div class="material-course">Digital Signal Processing</div>
                            </div>
                            <div class="material-body">
                                <div class="material-meta">
                                    <div class="material-type">
                                        <i class="fas fa-file-word text-primary"></i>
                                        <span>Word Document</span>
                                    </div>
                                    <div class="material-size">1.8 MB</div>
                                </div>
                                <p class="text-muted small mb-3">Laboratory exercises and practical assignments for digital signal processing course.</p>
                                <div class="material-actions">
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-download me-1"></i> Download
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-eye me-1"></i> Preview
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Video Lectures -->
                        <div class="material-card" data-course="current">
                            <div class="material-header">
                                <h6 class="material-title">Cybersecurity Fundamentals - Video Series</h6>
                                <div class="material-course">Cybersecurity Fundamentals</div>
                            </div>
                            <div class="material-body">
                                <div class="material-meta">
                                    <div class="material-type">
                                        <i class="fas fa-play-circle text-success"></i>
                                        <span>Video Content</span>
                                    </div>
                                    <div class="material-size">12 Videos</div>
                                </div>
                                <p class="text-muted small mb-3">Complete video lecture series covering network security, encryption, and threat analysis.</p>
                                <div class="material-actions">
                                    <button class="btn btn-success btn-sm">
                                        <i class="fas fa-play me-1"></i> Watch Online
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-download me-1"></i> Download
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Assignment Templates -->
                        <div class="material-card" data-course="completed">
                            <div class="material-header">
                                <h6 class="material-title">Assignment Templates</h6>
                                <div class="material-course">Basic Signals Course</div>
                            </div>
                            <div class="material-body">
                                <div class="material-meta">
                                    <div class="material-type">
                                        <i class="fas fa-file-archive text-warning"></i>
                                        <span>ZIP Archive</span>
                                    </div>
                                    <div class="material-size">850 KB</div>
                                </div>
                                <p class="text-muted small mb-3">Templates and guidelines for course assignments and project submissions.</p>
                                <div class="material-actions">
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-download me-1"></i> Download
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div class="empty-state d-none" id="noMaterials">
                        <i class="fas fa-book-open"></i>
                        <h4>No Study Materials</h4>
                        <p>No study materials are available for your current courses. Materials will appear here when uploaded by instructors.</p>
                    </div>
                </div>

                <!-- Academic Calendar Tab -->
                <div class="tab-pane fade" id="calendar" role="tabpanel" aria-labelledby="calendar-tab">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="calendar-container">
                                <div class="calendar-header">
                                    <div class="calendar-nav">
                                        <button onclick="previousMonth()">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                        <div class="calendar-month" id="currentMonth">December 2024</div>
                                        <button onclick="nextMonth()">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="calendar-body">
                                    <div class="calendar-grid">
                                        <!-- Calendar days will be generated by JavaScript -->
                                        <div class="calendar-day-header">Sun</div>
                                        <div class="calendar-day-header">Mon</div>
                                        <div class="calendar-day-header">Tue</div>
                                        <div class="calendar-day-header">Wed</div>
                                        <div class="calendar-day-header">Thu</div>
                                        <div class="calendar-day-header">Fri</div>
                                        <div class="calendar-day-header">Sat</div>

                                        <!-- Sample calendar days -->
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">1</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">2</div>
                                            <div class="calendar-event course">Course Start</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">3</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">4</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">5</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">6</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">7</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">8</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">9</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">10</div>
                                            <div class="calendar-event exam">Midterm Exam</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">11</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">12</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">13</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">14</div>
                                        </div>
                                        <div class="calendar-day today">
                                            <div class="calendar-day-number">15</div>
                                            <div class="calendar-event">Today</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">16</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">17</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">18</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">19</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">20</div>
                                            <div class="calendar-event deadline">Assignment Due</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">21</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">22</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">23</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">24</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">25</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">26</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">27</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">28</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">29</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">30</div>
                                            <div class="calendar-event course">Course End</div>
                                        </div>
                                        <div class="calendar-day">
                                            <div class="calendar-day-number">31</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-calendar-check me-2"></i> Upcoming Events
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="upcoming-events">
                                        <div class="event-item">
                                            <div class="event-date">
                                                <div class="event-day">20</div>
                                                <div class="event-month">Dec</div>
                                            </div>
                                            <div class="event-details">
                                                <h6 class="event-title">Assignment Submission</h6>
                                                <p class="event-course">Digital Signal Processing</p>
                                                <p class="event-time">Due: 11:59 PM</p>
                                            </div>
                                        </div>

                                        <div class="event-item">
                                            <div class="event-date">
                                                <div class="event-day">25</div>
                                                <div class="event-month">Dec</div>
                                            </div>
                                            <div class="event-details">
                                                <h6 class="event-title">Final Examination</h6>
                                                <p class="event-course">Cybersecurity Fundamentals</p>
                                                <p class="event-time">9:00 AM - 12:00 PM</p>
                                            </div>
                                        </div>

                                        <div class="event-item">
                                            <div class="event-date">
                                                <div class="event-day">30</div>
                                                <div class="event-month">Dec</div>
                                            </div>
                                            <div class="event-details">
                                                <h6 class="event-title">Course Completion</h6>
                                                <p class="event-course">Advanced Communication Systems</p>
                                                <p class="event-time">Certificate Available</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-info-circle me-2"></i> Legend
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="legend-items">
                                        <div class="legend-item">
                                            <div class="legend-color" style="background: var(--success-color);"></div>
                                            <span>Course Events</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color" style="background: var(--warning-color);"></div>
                                            <span>Examinations</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color" style="background: var(--danger-color);"></div>
                                            <span>Deadlines</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color" style="background: var(--primary-color);"></div>
                                            <span>General Events</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Overview Tab -->
                <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    <h4 class="mb-4">Profile Overview</h4>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-user me-2"></i> Personal Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Full Name</label>
                                                <div class="fw-bold">{{ student.surname }}, {{ student.other_names }}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Service Number</label>
                                                <div class="fw-bold">{{ student.service_number }}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Rank</label>
                                                <div class="fw-bold">{{ student.rank }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Date of Birth</label>
                                                <div class="fw-bold">{{ student.date_of_birth }}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Gender</label>
                                                <div class="fw-bold">{{ student.gender }}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Current Unit</label>
                                                <div class="fw-bold">{{ student.current_unit or 'Not Assigned' }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Analytics -->
                            <div class="analytics-grid">
                                <div class="analytics-card">
                                    <div class="analytics-header">
                                        <h6 class="analytics-title">Course Completion Rate</h6>
                                    </div>
                                    <div class="progress-ring" data-percentage="85">
                                        <svg>
                                            <circle class="background" cx="60" cy="60" r="50"></circle>
                                            <circle class="progress" cx="60" cy="60" r="50"></circle>
                                        </svg>
                                        <div class="percentage">85%</div>
                                    </div>
                                    <div class="text-center mt-3">
                                        <small class="text-muted">Excellent progress! Keep it up.</small>
                                    </div>
                                </div>

                                <div class="analytics-card">
                                    <div class="analytics-header">
                                        <h6 class="analytics-title">Monthly Activity</h6>
                                    </div>
                                    <div class="analytics-chart">
                                        <div class="chart-bar" data-value="0"></div>
                                        <div class="chart-bar" data-value="0"></div>
                                        <div class="chart-bar" data-value="0"></div>
                                        <div class="chart-bar" data-value="0"></div>
                                        <div class="chart-bar" data-value="0"></div>
                                        <div class="chart-bar" data-value="0"></div>
                                    </div>
                                    <div class="chart-labels">
                                        <span>Jul</span>
                                        <span>Aug</span>
                                        <span>Sep</span>
                                        <span>Oct</span>
                                        <span>Nov</span>
                                        <span>Dec</span>
                                    </div>
                                </div>

                                <div class="analytics-card">
                                    <div class="analytics-header">
                                        <h6 class="analytics-title">Average Grade</h6>
                                    </div>
                                    <div class="progress-ring" data-percentage="92">
                                        <svg>
                                            <circle class="background" cx="60" cy="60" r="50"></circle>
                                            <circle class="progress" cx="60" cy="60" r="50" style="stroke: var(--success-color);"></circle>
                                        </svg>
                                        <div class="percentage">A-</div>
                                    </div>
                                    <div class="text-center mt-3">
                                        <small class="text-muted">Outstanding academic performance!</small>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-chart-line me-2"></i> Learning Progress
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <div class="fs-2 fw-bold text-primary">{{ courses|length }}</div>
                                                <div class="text-muted">Total Courses</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <div class="fs-2 fw-bold text-success">{{ certificates|length }}</div>
                                                <div class="text-muted">Certificates</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <div class="fs-2 fw-bold text-warning">
                                                    {% set in_progress = courses|selectattr('status', 'equalto', 'in_progress')|list|length %}
                                                    {{ in_progress }}
                                                </div>
                                                <div class="text-muted">In Progress</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="fs-2 fw-bold text-info">
                                                {% set completed = courses|selectattr('status', 'equalto', 'completed')|list|length %}
                                                {{ completed }}
                                            </div>
                                            <div class="text-muted">Completed</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-cog me-2"></i> Account Settings
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('student.profile_edit') }}" class="btn btn-outline-primary">
                                            <i class="fas fa-edit me-2"></i> Edit Profile
                                        </a>
                                        <a href="{{ url_for('student.change_password') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-lock me-2"></i> Change Password
                                        </a>
                                        <a href="{{ url_for('student.documents') }}" class="btn btn-outline-info">
                                            <i class="fas fa-file-alt me-2"></i> My Documents
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-bell me-2"></i> Recent Activity
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        {% if student.last_login %}
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary"></div>
                                            <div class="timeline-content">
                                                <div class="fw-bold">Last Login</div>
                                                <div class="text-muted small">{{ student.last_login }}</div>
                                            </div>
                                        </div>
                                        {% endif %}

                                        {% if courses %}
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-success"></div>
                                            <div class="timeline-content">
                                                <div class="fw-bold">Course Registration</div>
                                                <div class="text-muted small">{{ courses|length }} course{{ 's' if courses|length != 1 else '' }} registered</div>
                                            </div>
                                        </div>
                                        {% endif %}

                                        {% if certificates %}
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-warning"></div>
                                            <div class="timeline-content">
                                                <div class="fw-bold">Certificates Earned</div>
                                                <div class="text-muted small">{{ certificates|length }} certificate{{ 's' if certificates|length != 1 else '' }} earned</div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize AOS (Animate On Scroll)
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Initialize Bootstrap tabs
        var triggerTabList = [].slice.call(document.querySelectorAll('#studentTabs button'));
        triggerTabList.forEach(function (triggerEl) {
            var tabTrigger = new bootstrap.Tab(triggerEl);

            triggerEl.addEventListener('click', function (event) {
                event.preventDefault();
                tabTrigger.show();

                // Refresh AOS animations when tab changes
                setTimeout(() => {
                    AOS.refresh();
                }, 100);
            });
        });

        // Course filtering functionality
        window.filterCourses = function(status) {
            const courseCards = document.querySelectorAll('.course-card[data-status]');
            const filterButtons = document.querySelectorAll('.btn-outline-primary, .btn-outline-success, .btn-outline-warning, .btn-outline-info');

            // Reset button states
            filterButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.classList.add('btn-outline-primary');
                btn.classList.remove('btn-primary');
            });

            // Set active button
            event.target.classList.add('active', 'btn-primary');
            event.target.classList.remove('btn-outline-primary');

            courseCards.forEach(card => {
                if (status === 'all' || card.dataset.status === status) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.5s ease-in-out';
                } else {
                    card.style.display = 'none';
                }
            });
        };

        // Course details modal functionality
        window.showCourseDetails = function(courseId) {
            // Find the course card to get course information
            const courseCard = document.querySelector(`[onclick="showCourseDetails('${courseId}')"]`).closest('.course-card');
            const courseTitle = courseCard.querySelector('.course-title').textContent;
            const courseDescription = courseCard.querySelector('.course-description').textContent;

            // Create modal content
            const modalContent = `
                <div class="modal fade" id="courseDetailsModal" tabindex="-1" aria-labelledby="courseDetailsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="courseDetailsModalLabel">
                                    <i class="fas fa-graduation-cap me-2"></i>${courseTitle}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="course-details">
                                    <h6>Course Description</h6>
                                    <p>${courseDescription}</p>

                                    <div class="row mt-4">
                                        <div class="col-md-6">
                                            <h6>Course Information</h6>
                                            <div class="course-meta">
                                                ${courseCard.querySelector('.course-meta').innerHTML}
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Registration</h6>
                                            <p>Click the "Register Now" button to enroll in this course.</p>
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Registration is subject to availability and meeting course prerequisites.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <a href="/student/register/${courseId}" class="btn btn-success">
                                    <i class="fas fa-plus-circle me-2"></i>Register for Course
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('courseDetailsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalContent);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('courseDetailsModal'));
            modal.show();

            // Clean up modal after it's hidden
            document.getElementById('courseDetailsModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        };

        // Add loading states to buttons
        document.querySelectorAll('.btn-course').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (!this.href || this.href === '#') {
                    e.preventDefault();
                    return;
                }

                const originalText = this.innerHTML;
                this.innerHTML = '<span class="loading"></span> Loading...';
                this.disabled = true;

                // Re-enable after 2 seconds (simulated loading)
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 2000);
            });
        });

        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-5px) scale(1)';
            });
        });

        // Add pulse animation to notification badges
        document.querySelectorAll('.notification-badge').forEach(badge => {
            badge.classList.add('pulse');
        });

        // Auto-refresh dashboard data every 5 minutes
        setInterval(function() {
            // This would typically make an AJAX call to refresh data
            console.log('Dashboard data refresh (simulated)');
        }, 300000); // 5 minutes

        // Add smooth scrolling to anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add keyboard navigation for tabs
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.getElementById('courses-tab').click();
                        break;
                    case '2':
                        e.preventDefault();
                        document.getElementById('certificates-tab').click();
                        break;
                    case '3':
                        e.preventDefault();
                        document.getElementById('register-tab').click();
                        break;
                    case '4':
                        e.preventDefault();
                        document.getElementById('notifications-tab').click();
                        break;
                    case '5':
                        e.preventDefault();
                        document.getElementById('materials-tab').click();
                        break;
                    case '6':
                        e.preventDefault();
                        document.getElementById('calendar-tab').click();
                        break;
                    case '7':
                        e.preventDefault();
                        document.getElementById('profile-tab').click();
                        break;
                }
            }
        });

        // Notifications functionality
        window.markAllAsRead = function() {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
            });
            updateNotificationCount();
            showNotification('All notifications marked as read', 'success');
        };

        window.clearNotifications = function() {
            if (confirm('Are you sure you want to clear all notifications?')) {
                document.querySelector('.notifications-container').innerHTML = '';
                document.getElementById('noNotifications').classList.remove('d-none');
                updateNotificationCount();
                showNotification('All notifications cleared', 'info');
            }
        };

        function updateNotificationCount() {
            const unreadCount = document.querySelectorAll('.notification-item.unread').length;
            const badge = document.getElementById('notificationCount');
            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.style.display = 'inline-block';
            } else {
                badge.style.display = 'none';
            }
        }

        // Study Materials functionality
        window.filterMaterials = function(filter) {
            const materials = document.querySelectorAll('.material-card');
            let visibleCount = 0;

            materials.forEach(material => {
                const course = material.dataset.course;
                if (filter === 'all' || course === filter) {
                    material.style.display = 'block';
                    visibleCount++;
                } else {
                    material.style.display = 'none';
                }
            });

            if (visibleCount === 0) {
                document.getElementById('noMaterials').classList.remove('d-none');
            } else {
                document.getElementById('noMaterials').classList.add('d-none');
            }
        };

        window.refreshMaterials = function() {
            showNotification('Refreshing study materials...', 'info');
            // Simulate refresh
            setTimeout(() => {
                showNotification('Study materials updated', 'success');
            }, 1000);
        };

        window.viewCourseMaterials = function(courseId) {
            // Switch to materials tab and filter by course
            document.getElementById('materials-tab').click();
            setTimeout(() => {
                // This would filter materials for the specific course
                showNotification('Showing materials for course ' + courseId, 'info');
            }, 300);
        };

        // Calendar functionality
        let currentDate = new Date();

        window.previousMonth = function() {
            currentDate.setMonth(currentDate.getMonth() - 1);
            updateCalendar();
        };

        window.nextMonth = function() {
            currentDate.setMonth(currentDate.getMonth() + 1);
            updateCalendar();
        };

        function updateCalendar() {
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'];
            const monthElement = document.getElementById('currentMonth');
            if (monthElement) {
                monthElement.textContent = monthNames[currentDate.getMonth()] + ' ' + currentDate.getFullYear();
            }
        }

        // Initialize calendar
        updateCalendar();

        // Performance analytics (simulated data)
        function initializeAnalytics() {
            // Create progress rings
            document.querySelectorAll('.progress-ring').forEach(ring => {
                const percentage = ring.dataset.percentage || 75;
                const circle = ring.querySelector('.progress');
                const radius = circle.r.baseVal.value;
                const circumference = radius * 2 * Math.PI;

                circle.style.strokeDasharray = circumference;
                circle.style.strokeDashoffset = circumference - (percentage / 100) * circumference;
            });

            // Animate chart bars
            document.querySelectorAll('.chart-bar').forEach((bar, index) => {
                const height = Math.random() * 150 + 20; // Random height for demo
                setTimeout(() => {
                    bar.style.height = height + 'px';
                    bar.setAttribute('data-value', Math.round(height / 10));
                }, index * 200);
            });
        }

        // Initialize analytics when profile tab is shown
        document.getElementById('profile-tab').addEventListener('click', function() {
            setTimeout(initializeAnalytics, 300);
        });

        // Add tooltips to action buttons
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Add search functionality (if search input exists)
        const searchInput = document.getElementById('courseSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const courseCards = document.querySelectorAll('.course-card');

                courseCards.forEach(card => {
                    const title = card.querySelector('.course-title').textContent.toLowerCase();
                    const description = card.querySelector('.course-description').textContent.toLowerCase();

                    if (title.includes(searchTerm) || description.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // Add progress bar animations
        document.querySelectorAll('.progress-bar').forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });

        // Add notification system
        window.showNotification = function(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        };

        // Add welcome message for first-time users
        if ({{ 'true' if courses|length == 0 else 'false' }}) {
            setTimeout(() => {
                {% if available_courses %}
                    showNotification('Welcome to your student dashboard! {{ available_courses|length }} course{{ "s are" if available_courses|length != 1 else " is" }} available for registration.', 'info');
                {% else %}
                    showNotification('Welcome to your student dashboard! No courses are currently available for registration.', 'info');
                {% endif %}
            }, 1000);
        }

        // Check for registration success message in URL
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('registered') === 'success') {
            setTimeout(() => {
                showNotification('🎉 Course registration successful! Welcome to your new course.', 'success');
                // Remove the parameter from URL
                window.history.replaceState({}, document.title, window.location.pathname);
            }, 500);
        }

        // Add course registration tracking
        document.querySelectorAll('a[href*="/student/register/"]').forEach(link => {
            link.addEventListener('click', function() {
                // Track course registration attempts
                const courseId = this.href.split('/').pop();
                console.log('Attempting to register for course:', courseId);

                // Show loading notification
                showNotification('Redirecting to course registration...', 'info');
            });
        });
    });

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #dee2e6;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            border-left: 3px solid var(--primary-color);
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}
