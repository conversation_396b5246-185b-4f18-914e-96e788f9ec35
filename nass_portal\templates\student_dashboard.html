{% extends 'base.html' %}

{% block title %}Student Portal - Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/styles.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/modern.css', v='1.1') }}">
<link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
<style>
    :root {
        --primary-color: #3c78c3;
        --secondary-color: #2c5aa0;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --info-color: #17a2b8;
        --danger-color: #dc3545;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --border-radius: 12px;
        --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    body {
        background-color: #f5f7fa;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .dashboard-container {
        min-height: 100vh;
        padding: 20px 0;
    }

    /* Header Section */
    .student-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 40px 30px;
        margin-bottom: 30px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        position: relative;
        overflow: hidden;
    }

    .student-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .student-photo {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        transition: var(--transition);
    }

    .student-photo:hover {
        transform: scale(1.05);
        border-color: rgba(255, 255, 255, 0.6);
    }

    .student-name {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .student-rank {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .student-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .info-item {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        backdrop-filter: blur(10px);
        transition: var(--transition);
    }

    .info-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }

    .info-label {
        font-size: 0.85rem;
        opacity: 0.8;
        margin-bottom: 5px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        font-size: 1.1rem;
        font-weight: 600;
    }

    /* Quick Actions */
    .quick-actions {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 25px;
        height: 100%;
    }

    .quick-actions h5 {
        color: var(--dark-color);
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .action-btn {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 15px 20px;
        margin-bottom: 10px;
        border: none;
        border-radius: 8px;
        background: var(--light-color);
        color: var(--dark-color);
        text-decoration: none;
        transition: var(--transition);
        font-weight: 500;
    }

    .action-btn:hover {
        background: var(--primary-color);
        color: white;
        transform: translateX(5px);
        text-decoration: none;
    }

    .action-btn.danger:hover {
        background: var(--danger-color);
    }

    .action-btn i {
        width: 20px;
        text-align: center;
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 25px;
        box-shadow: var(--box-shadow);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-color);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-card.success::before { background: var(--success-color); }
    .stat-card.warning::before { background: var(--warning-color); }
    .stat-card.info::before { background: var(--info-color); }
    .stat-card.danger::before { background: var(--danger-color); }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 15px;
        background: var(--light-color);
        color: var(--primary-color);
    }

    .stat-card.success .stat-icon { background: rgba(40, 167, 69, 0.1); color: var(--success-color); }
    .stat-card.warning .stat-icon { background: rgba(255, 193, 7, 0.1); color: var(--warning-color); }
    .stat-card.info .stat-icon { background: rgba(23, 162, 184, 0.1); color: var(--info-color); }
    .stat-card.danger .stat-icon { background: rgba(220, 53, 69, 0.1); color: var(--danger-color); }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .stat-change {
        font-size: 0.85rem;
        font-weight: 600;
    }

    .stat-change.positive { color: var(--success-color); }
    .stat-change.negative { color: var(--danger-color); }

    /* Main Content */
    .main-content {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        overflow: hidden;
    }

    .content-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 20px 30px;
        border-bottom: 1px solid #dee2e6;
    }

    .nav-tabs {
        border: none;
        background: transparent;
    }

    .nav-tabs .nav-link {
        border: none;
        background: transparent;
        color: #6c757d;
        font-weight: 600;
        padding: 15px 25px;
        margin-right: 10px;
        border-radius: 8px 8px 0 0;
        transition: var(--transition);
    }

    .nav-tabs .nav-link:hover {
        background: rgba(60, 120, 195, 0.1);
        color: var(--primary-color);
    }

    .nav-tabs .nav-link.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .tab-content {
        padding: 30px;
    }

    /* Course Cards */
    .course-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 25px;
    }

    .course-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        position: relative;
    }

    .course-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-color);
    }

    .course-card-header {
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;
    }

    .course-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-completed {
        background: var(--success-color);
        color: white;
    }

    .status-in-progress {
        background: var(--warning-color);
        color: var(--dark-color);
    }

    .status-registered {
        background: var(--info-color);
        color: white;
    }

    .course-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 10px;
        padding-right: 100px;
    }

    .course-description {
        color: #6c757d;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .course-card-body {
        padding: 20px;
    }

    .course-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .meta-item i {
        color: var(--primary-color);
    }

    .course-progress {
        margin-bottom: 20px;
    }

    .progress {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
    }

    .progress-bar {
        border-radius: 4px;
        transition: width 0.6s ease;
    }

    .course-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn-course {
        flex: 1;
        min-width: 120px;
        padding: 10px 15px;
        border-radius: 8px;
        font-weight: 600;
        transition: var(--transition);
        border: none;
        text-decoration: none;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-course:hover {
        transform: translateY(-2px);
        text-decoration: none;
    }

    .certificate-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: var(--success-color);
        color: white;
        padding: 8px;
        border-radius: 50%;
        font-size: 0.9rem;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    /* Empty States */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }

    .empty-state h4 {
        color: var(--dark-color);
        margin-bottom: 15px;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .course-grid {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 10px 0;
        }

        .student-header {
            padding: 30px 20px;
            margin-bottom: 20px;
        }

        .student-name {
            font-size: 1.8rem;
        }

        .student-info {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .course-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .tab-content {
            padding: 20px;
        }

        .nav-tabs .nav-link {
            padding: 12px 15px;
            margin-right: 5px;
            font-size: 0.9rem;
        }

        .course-actions {
            flex-direction: column;
        }

        .btn-course {
            min-width: auto;
        }
    }

    @media (max-width: 576px) {
        .student-header {
            padding: 20px 15px;
        }

        .student-name {
            font-size: 1.5rem;
        }

        .student-photo {
            width: 100px;
            height: 100px;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .nav-tabs {
            flex-wrap: nowrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .nav-tabs .nav-link {
            white-space: nowrap;
            margin-right: 10px;
        }
    }

    /* Loading Animation */
    .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Notification Badge */
    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }

    /* Pulse Animation */
    .pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="container-fluid">
        <!-- Student Header -->
        <div class="student-header" data-aos="fade-down">
            <div class="row align-items-center">
                <div class="col-lg-2 col-md-3 text-center mb-3 mb-md-0">
                    {% if student.passport_photo %}
                    <img src="{{ url_for('static', filename='uploads/passport_photos/' + student.passport_photo) }}" class="student-photo" alt="Student Photo">
                    {% else %}
                    <img src="{{ url_for('static', filename='images/placeholder-user.png') }}" class="student-photo" alt="Student Photo">
                    {% endif %}
                </div>
                <div class="col-lg-6 col-md-9 mb-3 mb-lg-0">
                    <h1 class="student-name">{{ student.surname }}, {{ student.other_names }}</h1>
                    <div class="student-rank">{{ student.rank }}</div>
                    <div class="student-info">
                        <div class="info-item">
                            <div class="info-label">Service Number</div>
                            <div class="info-value">{{ student.service_number }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Current Unit</div>
                            <div class="info-value">{{ student.current_unit or 'Not Assigned' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Years in Service</div>
                            <div class="info-value">{{ student.years_in_service or 'N/A' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Corps</div>
                            <div class="info-value">{{ student.corps or 'Not Assigned' }}</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="quick-actions" data-aos="fade-left" data-aos-delay="200">
                        <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                        <a href="{{ url_for('student.profile') }}" class="action-btn">
                            <i class="fas fa-user"></i> View Full Profile
                        </a>
                        <a href="{{ url_for('student.documents') }}" class="action-btn">
                            <i class="fas fa-file-alt"></i> My Documents
                        </a>
                        <a href="{{ url_for('student.profile_edit') }}" class="action-btn">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                        <a href="{{ url_for('student.change_password') }}" class="action-btn">
                            <i class="fas fa-lock"></i> Change Password
                        </a>
                        <a href="{{ url_for('student.logout') }}" class="action-btn danger">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="row mb-4">
                    <div class="col-12">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert" data-aos="fade-up">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        {% endwith %}

        <!-- Statistics Cards -->
        <div class="stats-grid" data-aos="fade-up" data-aos-delay="300">
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stat-number">{{ courses|length }}</div>
                <div class="stat-label">Total Courses</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> Active Enrollments
                </div>
            </div>

            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="stat-number">{{ certificates|length }}</div>
                <div class="stat-label">Certificates Earned</div>
                <div class="stat-change positive">
                    <i class="fas fa-award"></i> Achievements
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number">
                    {% set in_progress = courses|selectattr('status', 'equalto', 'in_progress')|list|length %}
                    {{ in_progress }}
                </div>
                <div class="stat-label">In Progress</div>
                <div class="stat-change">
                    <i class="fas fa-play"></i> Active Learning
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-number">
                    {% if registration_deadline %}
                        <i class="fas fa-calendar-alt"></i>
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="stat-label">Registration Status</div>
                <div class="stat-change">
                    {% if registration_deadline %}
                        <i class="fas fa-check-circle"></i> Open
                    {% else %}
                        <i class="fas fa-times-circle"></i> Closed
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content" data-aos="fade-up" data-aos-delay="400">
            <div class="content-header">
                <ul class="nav nav-tabs" id="studentTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab" aria-controls="courses" aria-selected="true">
                            <i class="fas fa-graduation-cap me-2"></i> My Courses
                            {% if courses %}
                                <span class="badge bg-light text-dark ms-2">{{ courses|length }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="certificates-tab" data-bs-toggle="tab" data-bs-target="#certificates" type="button" role="tab" aria-controls="certificates" aria-selected="false">
                            <i class="fas fa-certificate me-2"></i> Certificates
                            {% if certificates %}
                                <span class="badge bg-light text-dark ms-2">{{ certificates|length }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register" type="button" role="tab" aria-controls="register" aria-selected="false">
                            <i class="fas fa-plus-circle me-2"></i> Register for New Course
                            {% if available_courses %}
                                <span class="badge bg-warning text-dark ms-2">{{ available_courses|length }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false">
                            <i class="fas fa-user me-2"></i> Profile Overview
                        </button>
                    </li>
                </ul>
            </div>

            <div class="tab-content" id="studentTabsContent">
                <!-- Courses Tab -->
                <div class="tab-pane fade show active" id="courses" role="tabpanel" aria-labelledby="courses-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">My Course History</h4>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="filterCourses('all')">All</button>
                            <button class="btn btn-outline-success btn-sm" onclick="filterCourses('completed')">Completed</button>
                            <button class="btn btn-outline-warning btn-sm" onclick="filterCourses('in_progress')">In Progress</button>
                            <button class="btn btn-outline-info btn-sm" onclick="filterCourses('registered')">Registered</button>
                        </div>
                    </div>

                    {% if courses %}
                        <div class="course-grid">
                            {% for course in courses %}
                                <div class="course-card" data-status="{{ course.status }}" data-aos="fade-up" data-aos-delay="{{ loop.index * 100 }}">
                                    {% if course.status == 'completed' and course.has_certificate %}
                                        <div class="certificate-badge" title="Certificate Available">
                                            <i class="fas fa-award"></i>
                                        </div>
                                    {% endif %}

                                    <div class="course-card-header">
                                        <span class="course-status status-{{ course.status }}">
                                            {{ course.status|replace('_', ' ')|title }}
                                        </span>
                                        <h5 class="course-title">{{ course.name }}</h5>
                                        <p class="course-description">{{ course.description[:100] }}{% if course.description|length > 100 %}...{% endif %}</p>
                                    </div>

                                    <div class="course-card-body">
                                        <div class="course-meta">
                                            {% if course.start_date and course.end_date %}
                                            <div class="meta-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                <span>{{ course.start_date }} - {{ course.end_date }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.duration %}
                                            <div class="meta-item">
                                                <i class="fas fa-clock"></i>
                                                <span>{{ course.duration }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.level %}
                                            <div class="meta-item">
                                                <i class="fas fa-layer-group"></i>
                                                <span>{{ course.level }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.department %}
                                            <div class="meta-item">
                                                <i class="fas fa-building"></i>
                                                <span>{{ course.department }}</span>
                                            </div>
                                            {% endif %}
                                        </div>

                                        {% if course.status == 'in_progress' %}
                                        <div class="course-progress">
                                            <div class="d-flex justify-content-between mb-1">
                                                <small>Progress</small>
                                                <small>{{ course.progress or 0 }}%</small>
                                            </div>
                                            <div class="progress">
                                                <div class="progress-bar bg-warning" role="progressbar" style="width: {{ course.progress or 0 }}%"></div>
                                            </div>
                                        </div>
                                        {% endif %}

                                        <div class="course-actions">
                                            <a href="{{ url_for('student.course_detail', course_id=course.id) }}" class="btn-course btn btn-outline-primary">
                                                <i class="fas fa-eye"></i> View Details
                                            </a>

                                            {% if course.status == 'completed' and course.has_certificate %}
                                                <a href="{{ url_for('student.certificate', certificate_id=course.certificate_id) }}" class="btn-course btn btn-success">
                                                    <i class="fas fa-download"></i> Certificate
                                                </a>
                                            {% elif course.status == 'in_progress' %}
                                                <a href="#" class="btn-course btn btn-warning">
                                                    <i class="fas fa-play"></i> Continue
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-graduation-cap"></i>
                            <h4>No Courses Yet</h4>
                            <p>You haven't registered for any courses yet. Start your learning journey by registering for a course!</p>
                            <button class="btn btn-primary" onclick="document.getElementById('register-tab').click()">
                                <i class="fas fa-plus-circle me-2"></i> Register for Course
                            </button>
                        </div>
                    {% endif %}
                </div>

                <!-- Certificates Tab -->
                <div class="tab-pane fade" id="certificates" role="tabpanel" aria-labelledby="certificates-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">My Certificates</h4>
                        <div class="text-muted">
                            <i class="fas fa-award me-1"></i> {{ certificates|length }} Certificate{{ 's' if certificates|length != 1 else '' }}
                        </div>
                    </div>

                    {% if certificates %}
                        <div class="course-grid">
                            {% for cert in certificates %}
                                <div class="course-card" data-aos="fade-up" data-aos-delay="{{ loop.index * 100 }}">
                                    <div class="certificate-badge">
                                        <i class="fas fa-award"></i>
                                    </div>

                                    <div class="course-card-header">
                                        <span class="course-status status-completed">Certified</span>
                                        <h5 class="course-title">{{ cert.course_name }}</h5>
                                        <p class="course-description">Certificate of completion for {{ cert.course_name }}</p>
                                    </div>

                                    <div class="course-card-body">
                                        <div class="course-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-calendar-check"></i>
                                                <span>Issued: {{ cert.issue_date }}</span>
                                            </div>
                                            {% if cert.certificate_number %}
                                            <div class="meta-item">
                                                <i class="fas fa-hashtag"></i>
                                                <span>{{ cert.certificate_number }}</span>
                                            </div>
                                            {% endif %}
                                            {% if cert.grade %}
                                            <div class="meta-item">
                                                <i class="fas fa-star"></i>
                                                <span>Grade: {{ cert.grade }}</span>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <div class="course-actions">
                                            <a href="{{ url_for('student.certificate', certificate_id=cert.id) }}" class="btn-course btn btn-success">
                                                <i class="fas fa-download"></i> Download
                                            </a>
                                            <a href="{{ url_for('student.certificate_view', certificate_id=cert.id) }}" class="btn-course btn btn-outline-primary" target="_blank">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-certificate"></i>
                            <h4>No Certificates Yet</h4>
                            <p>Complete courses to earn certificates. Your achievements will be displayed here.</p>
                            <button class="btn btn-primary" onclick="document.getElementById('courses-tab').click()">
                                <i class="fas fa-graduation-cap me-2"></i> View My Courses
                            </button>
                        </div>
                    {% endif %}
                </div>

                <!-- Register for New Course Tab -->
                <div class="tab-pane fade" id="register" role="tabpanel" aria-labelledby="register-tab">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">Register for a New Course</h4>
                        {% if registration_deadline %}
                        <div class="text-muted">
                            <i class="fas fa-clock me-1"></i> Deadline: {{ registration_deadline }}
                        </div>
                        {% endif %}
                    </div>

                    {% if available_courses %}
                        {% if registration_deadline %}
                        <div class="alert alert-info mb-4" data-aos="fade-up">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle me-3 fs-4"></i>
                                <div>
                                    <strong>Registration Open!</strong><br>
                                    Registration for the current quarter is open until <strong>{{ registration_deadline }}</strong>.
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="course-grid">
                            {% for course in available_courses %}
                                <div class="course-card" data-aos="fade-up" data-aos-delay="{{ loop.index * 100 }}">
                                    <div class="course-card-header">
                                        <span class="course-status" style="background: var(--success-color); color: white;">Available</span>
                                        <h5 class="course-title">{{ course.name }}</h5>
                                        <p class="course-description">{{ course.description[:100] }}{% if course.description|length > 100 %}...{% endif %}</p>
                                    </div>

                                    <div class="course-card-body">
                                        <div class="course-meta">
                                            {% if course.start_date and course.end_date %}
                                            <div class="meta-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                <span>{{ course.start_date }} - {{ course.end_date }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.duration %}
                                            <div class="meta-item">
                                                <i class="fas fa-clock"></i>
                                                <span>{{ course.duration }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.level %}
                                            <div class="meta-item">
                                                <i class="fas fa-layer-group"></i>
                                                <span>{{ course.level }}</span>
                                            </div>
                                            {% endif %}
                                            {% if course.max_students %}
                                            <div class="meta-item">
                                                <i class="fas fa-users"></i>
                                                <span>{{ course.available_slots or course.max_students }} slots available</span>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <div class="course-actions">
                                            <a href="{{ url_for('main.registration') }}" class="btn-course btn btn-success">
                                                <i class="fas fa-plus-circle"></i> Register Now
                                            </a>
                                            <a href="#" class="btn-course btn btn-outline-primary" onclick="showCourseDetails('{{ course.id }}')">
                                                <i class="fas fa-info-circle"></i> Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-calendar-times"></i>
                            <h4>No Courses Available</h4>
                            <p>There are no courses available for registration at this time. Please check back later or contact the administration.</p>
                            <a href="{{ url_for('main.contact') }}" class="btn btn-outline-primary">
                                <i class="fas fa-envelope me-2"></i> Contact Administration
                            </a>
                        </div>
                    {% endif %}
                </div>

                <!-- Profile Overview Tab -->
                <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    <h4 class="mb-4">Profile Overview</h4>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-user me-2"></i> Personal Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Full Name</label>
                                                <div class="fw-bold">{{ student.surname }}, {{ student.other_names }}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Service Number</label>
                                                <div class="fw-bold">{{ student.service_number }}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Rank</label>
                                                <div class="fw-bold">{{ student.rank }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Date of Birth</label>
                                                <div class="fw-bold">{{ student.date_of_birth }}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Gender</label>
                                                <div class="fw-bold">{{ student.gender }}</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Current Unit</label>
                                                <div class="fw-bold">{{ student.current_unit or 'Not Assigned' }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-chart-line me-2"></i> Learning Progress
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <div class="fs-2 fw-bold text-primary">{{ courses|length }}</div>
                                                <div class="text-muted">Total Courses</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <div class="fs-2 fw-bold text-success">{{ certificates|length }}</div>
                                                <div class="text-muted">Certificates</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="border-end">
                                                <div class="fs-2 fw-bold text-warning">
                                                    {% set in_progress = courses|selectattr('status', 'equalto', 'in_progress')|list|length %}
                                                    {{ in_progress }}
                                                </div>
                                                <div class="text-muted">In Progress</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="fs-2 fw-bold text-info">
                                                {% set completed = courses|selectattr('status', 'equalto', 'completed')|list|length %}
                                                {{ completed }}
                                            </div>
                                            <div class="text-muted">Completed</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-cog me-2"></i> Account Settings
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('student.profile_edit') }}" class="btn btn-outline-primary">
                                            <i class="fas fa-edit me-2"></i> Edit Profile
                                        </a>
                                        <a href="{{ url_for('student.change_password') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-lock me-2"></i> Change Password
                                        </a>
                                        <a href="{{ url_for('student.documents') }}" class="btn btn-outline-info">
                                            <i class="fas fa-file-alt me-2"></i> My Documents
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-bell me-2"></i> Recent Activity
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        {% if student.last_login %}
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary"></div>
                                            <div class="timeline-content">
                                                <div class="fw-bold">Last Login</div>
                                                <div class="text-muted small">{{ student.last_login }}</div>
                                            </div>
                                        </div>
                                        {% endif %}

                                        {% if courses %}
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-success"></div>
                                            <div class="timeline-content">
                                                <div class="fw-bold">Course Registration</div>
                                                <div class="text-muted small">{{ courses|length }} course{{ 's' if courses|length != 1 else '' }} registered</div>
                                            </div>
                                        </div>
                                        {% endif %}

                                        {% if certificates %}
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-warning"></div>
                                            <div class="timeline-content">
                                                <div class="fw-bold">Certificates Earned</div>
                                                <div class="text-muted small">{{ certificates|length }} certificate{{ 's' if certificates|length != 1 else '' }} earned</div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize AOS (Animate On Scroll)
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Initialize Bootstrap tabs
        var triggerTabList = [].slice.call(document.querySelectorAll('#studentTabs button'));
        triggerTabList.forEach(function (triggerEl) {
            var tabTrigger = new bootstrap.Tab(triggerEl);

            triggerEl.addEventListener('click', function (event) {
                event.preventDefault();
                tabTrigger.show();

                // Refresh AOS animations when tab changes
                setTimeout(() => {
                    AOS.refresh();
                }, 100);
            });
        });

        // Course filtering functionality
        window.filterCourses = function(status) {
            const courseCards = document.querySelectorAll('.course-card[data-status]');
            const filterButtons = document.querySelectorAll('.btn-outline-primary, .btn-outline-success, .btn-outline-warning, .btn-outline-info');

            // Reset button states
            filterButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.classList.add('btn-outline-primary');
                btn.classList.remove('btn-primary');
            });

            // Set active button
            event.target.classList.add('active', 'btn-primary');
            event.target.classList.remove('btn-outline-primary');

            courseCards.forEach(card => {
                if (status === 'all' || card.dataset.status === status) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.5s ease-in-out';
                } else {
                    card.style.display = 'none';
                }
            });
        };

        // Course details modal functionality
        window.showCourseDetails = function(courseId) {
            // This would typically open a modal with course details
            // For now, we'll just show an alert
            alert('Course details for course ID: ' + courseId + '\n\nThis would open a detailed view of the course.');
        };

        // Add loading states to buttons
        document.querySelectorAll('.btn-course').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (!this.href || this.href === '#') {
                    e.preventDefault();
                    return;
                }

                const originalText = this.innerHTML;
                this.innerHTML = '<span class="loading"></span> Loading...';
                this.disabled = true;

                // Re-enable after 2 seconds (simulated loading)
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 2000);
            });
        });

        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-5px) scale(1)';
            });
        });

        // Add pulse animation to notification badges
        document.querySelectorAll('.notification-badge').forEach(badge => {
            badge.classList.add('pulse');
        });

        // Auto-refresh dashboard data every 5 minutes
        setInterval(function() {
            // This would typically make an AJAX call to refresh data
            console.log('Dashboard data refresh (simulated)');
        }, 300000); // 5 minutes

        // Add smooth scrolling to anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add keyboard navigation for tabs
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.getElementById('courses-tab').click();
                        break;
                    case '2':
                        e.preventDefault();
                        document.getElementById('certificates-tab').click();
                        break;
                    case '3':
                        e.preventDefault();
                        document.getElementById('register-tab').click();
                        break;
                    case '4':
                        e.preventDefault();
                        document.getElementById('profile-tab').click();
                        break;
                }
            }
        });

        // Add tooltips to action buttons
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Add search functionality (if search input exists)
        const searchInput = document.getElementById('courseSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const courseCards = document.querySelectorAll('.course-card');

                courseCards.forEach(card => {
                    const title = card.querySelector('.course-title').textContent.toLowerCase();
                    const description = card.querySelector('.course-description').textContent.toLowerCase();

                    if (title.includes(searchTerm) || description.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // Add progress bar animations
        document.querySelectorAll('.progress-bar').forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });

        // Add notification system
        window.showNotification = function(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        };

        // Add welcome message for first-time users
        if ({{ 'true' if courses|length == 0 else 'false' }}) {
            setTimeout(() => {
                showNotification('Welcome to your student dashboard! Start by registering for a course.', 'info');
            }, 1000);
        }
    });

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #dee2e6;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            border-left: 3px solid var(--primary-color);
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}
