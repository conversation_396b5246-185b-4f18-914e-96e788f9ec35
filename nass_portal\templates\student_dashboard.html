{% extends 'base.html' %}

{% block title %}Student Portal - Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/styles.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/modern.css', v='1.1') }}">
<style>
    .student-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .student-photo {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        object-fit: cover;
        border: 5px solid white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .student-name {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .student-rank {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 15px;
    }

    .student-info {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }

    .info-item {
        background-color: white;
        border-radius: 8px;
        padding: 10px 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .info-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 5px;
    }
    
    .course-card {
        transition: transform 0.3s, box-shadow 0.3s;
        height: 100%;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .course-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .course-status {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-in-progress {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-registered {
        background-color: #cce5ff;
        color: #004085;
    }
    
    .certificate-badge {
        position: absolute;
        bottom: 10px;
        right: 10px;
        background-color: #28a745;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .course-date {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .tab-pane {
        padding: 20px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Student Header -->
    <div class="student-header">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                {% if student.passport_photo %}
                <img src="{{ url_for('static', filename='uploads/passport_photos/' + student.passport_photo) }}" class="student-photo" alt="Student Photo">
                {% else %}
                <img src="{{ url_for('static', filename='images/placeholder-user.png') }}" class="student-photo" alt="Student Photo">
                {% endif %}
            </div>
            <div class="col-md-6">
                <h1 class="student-name">{{ student.surname }}, {{ student.other_names }}</h1>
                <div class="student-rank">{{ student.rank }}</div>
                <div class="student-info">
                    <div class="info-item">
                        <div class="info-label">Service Number</div>
                        <div class="info-value">{{ student.service_number }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Current Unit</div>
                        <div class="info-value">{{ student.current_unit }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Years in Service</div>
                        <div class="info-value">{{ student.years_in_service }}</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">Quick Actions</h5>
                        <div class="list-group list-group-flush">
                            <a href="{{ url_for('student.profile') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-user me-2"></i> View Full Profile
                            </a>
                            <a href="{{ url_for('student.documents') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-file-alt me-2"></i> My Documents
                            </a>
                            <a href="{{ url_for('student.profile') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-print me-2"></i> Print Profile
                            </a>
                            <a href="{{ url_for('student.logout') }}" class="list-group-item list-group-item-action text-danger">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="row mb-4">
                <div class="col-12">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="studentTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab" aria-controls="courses" aria-selected="true">
                                <i class="fas fa-graduation-cap me-2"></i> My Courses
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="certificates-tab" data-bs-toggle="tab" data-bs-target="#certificates" type="button" role="tab" aria-controls="certificates" aria-selected="false">
                                <i class="fas fa-certificate me-2"></i> Certificates
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register" type="button" role="tab" aria-controls="register" aria-selected="false">
                                <i class="fas fa-plus-circle me-2"></i> Register for New Course
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="studentTabsContent">
                        <!-- Courses Tab -->
                        <div class="tab-pane fade show active" id="courses" role="tabpanel" aria-labelledby="courses-tab">
                            <h4 class="mb-4">My Course History</h4>
                            
                            {% if courses %}
                                <div class="row">
                                    {% for course in courses %}
                                        <div class="col-md-4 mb-4">
                                            <div class="card course-card">
                                                <div class="card-body position-relative">
                                                    <span class="course-status {% if course.status == 'completed' %}status-completed{% elif course.status == 'in_progress' %}status-in-progress{% else %}status-registered{% endif %}">
                                                        {{ course.status|capitalize }}
                                                    </span>
                                                    
                                                    <h5 class="card-title">{{ course.name }}</h5>
                                                    <p class="card-text">{{ course.description }}</p>
                                                    
                                                    <div class="course-date">
                                                        <i class="fas fa-calendar-alt me-1"></i> {{ course.start_date }} - {{ course.end_date }}
                                                    </div>
                                                    
                                                    {% if course.status == 'completed' and course.has_certificate %}
                                                        <span class="certificate-badge" title="Certificate Available">
                                                            <i class="fas fa-award"></i>
                                                        </span>
                                                    {% endif %}
                                                </div>
                                                <div class="card-footer bg-transparent">
                                                    <a href="{{ url_for('student.course_detail', course_id=course.id) }}" class="btn btn-sm btn-outline-primary">View Details</a>

                                                    {% if course.status == 'completed' and course.has_certificate %}
                                                        <a href="{{ url_for('student.certificate', certificate_id=course.certificate_id) }}" class="btn btn-sm btn-success float-end">
                                                            <i class="fas fa-download me-1"></i> Certificate
                                                        </a>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> You haven't registered for any courses yet.
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Certificates Tab -->
                        <div class="tab-pane fade" id="certificates" role="tabpanel" aria-labelledby="certificates-tab">
                            <h4 class="mb-4">My Certificates</h4>
                            
                            {% if certificates %}
                                <div class="row">
                                    {% for cert in certificates %}
                                        <div class="col-md-4 mb-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h5 class="card-title">{{ cert.course_name }}</h5>
                                                    <p class="card-text">Issued on: {{ cert.issue_date }}</p>
                                                    <a href="{{ url_for('student.certificate', certificate_id=cert.id) }}" class="btn btn-primary">
                                                        <i class="fas fa-download me-2"></i> Download
                                                    </a>
                                                    <a href="{{ url_for('student.certificate_view', certificate_id=cert.id) }}" class="btn btn-outline-secondary" target="_blank">
                                                        <i class="fas fa-eye me-2"></i> View
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> You don't have any certificates yet.
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Register for New Course Tab -->
                        <div class="tab-pane fade" id="register" role="tabpanel" aria-labelledby="register-tab">
                            <h4 class="mb-4">Register for a New Course</h4>
                            
                            {% if available_courses %}
                                <div class="alert alert-info mb-4">
                                    <i class="fas fa-info-circle me-2"></i> Registration for the current quarter is open until <strong>{{ registration_deadline }}</strong>.
                                </div>
                                
                                <div class="row">
                                    {% for course in available_courses %}
                                        <div class="col-md-4 mb-4">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <h5 class="card-title">{{ course.name }}</h5>
                                                    <p class="card-text">{{ course.description }}</p>
                                                    <p class="text-muted">
                                                        <i class="fas fa-calendar-alt me-1"></i> {{ course.start_date }} - {{ course.end_date }}<br>
                                                        <i class="fas fa-users me-1"></i> {{ course.available_slots }} slots available
                                                    </p>
                                                </div>
                                                <div class="card-footer bg-transparent">
                                                    <a href="{{ url_for('main.registration') }}" class="btn btn-primary">Register Now</a>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> There are no courses available for registration at this time. Please check back later.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Bootstrap tabs
        var triggerTabList = [].slice.call(document.querySelectorAll('#studentTabs button'))
        triggerTabList.forEach(function (triggerEl) {
            var tabTrigger = new bootstrap.Tab(triggerEl)
            
            triggerEl.addEventListener('click', function (event) {
                event.preventDefault()
                tabTrigger.show()
            })
        })
    });
</script>
{% endblock %}
