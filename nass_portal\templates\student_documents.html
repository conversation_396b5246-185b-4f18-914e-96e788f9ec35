{% extends 'base.html' %}

{% block title %}My Documents - Student Portal{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/styles.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/modern.css', v='1.1') }}">
<style>
    .documents-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .documents-header {
        background-color: #3c78c3;
        color: white;
        padding: 20px;
        position: relative;
    }
    
    .documents-body {
        padding: 30px;
    }
    
    .sidebar-nav {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }
    
    .sidebar-nav .nav-link {
        color: #495057;
        padding: 10px 15px;
        margin-bottom: 5px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }
    
    .sidebar-nav .nav-link:hover {
        background-color: #e9ecef;
        color: #3c78c3;
    }
    
    .sidebar-nav .nav-link.active {
        background-color: #3c78c3;
        color: white;
    }
    
    .document-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .document-item:hover {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-color: #3c78c3;
    }
    
    .document-icon {
        font-size: 2rem;
        color: #3c78c3;
        margin-bottom: 10px;
    }
    
    .document-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .document-info {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .document-actions {
        margin-top: 15px;
    }
    
    .no-documents {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .no-documents i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 mb-4">
            <div class="sidebar-nav">
                <h5 class="mb-3">Student Portal</h5>
                <div class="nav flex-column">
                    <a href="{{ url_for('student.dashboard') }}" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{{ url_for('student.profile') }}" class="nav-link">
                        <i class="fas fa-user"></i> My Profile
                    </a>
                    <a href="{{ url_for('student.documents') }}" class="nav-link active">
                        <i class="fas fa-file-alt"></i> My Documents
                    </a>
                    <a href="{{ url_for('student.dashboard') }}#courses" class="nav-link">
                        <i class="fas fa-graduation-cap"></i> My Courses
                    </a>
                    <a href="{{ url_for('student.dashboard') }}#certificates" class="nav-link">
                        <i class="fas fa-certificate"></i> My Certificates
                    </a>
                    <a href="{{ url_for('student.logout') }}" class="nav-link text-danger">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Documents Card -->
            <div class="documents-card">
                <div class="documents-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="fas fa-file-alt me-2"></i> My Documents</h2>
                            <p class="mb-0">View and manage your uploaded documents</p>
                        </div>
                        <div>
                            <span class="badge bg-light text-dark fs-6">
                                {{ documents|length }} Document{{ 's' if documents|length != 1 else '' }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="documents-body">
                    {% if documents %}
                        <div class="row">
                            {% for document in documents %}
                                <div class="col-md-6 mb-3">
                                    <div class="document-item">
                                        <div class="text-center">
                                            <div class="document-icon">
                                                <i class="fas fa-file-pdf"></i>
                                            </div>
                                        </div>
                                        
                                        <div class="document-name">{{ document.requirement_name }}</div>
                                        <div class="document-info">
                                            <div><i class="fas fa-file me-1"></i> {{ document.original_filename }}</div>
                                            <div><i class="fas fa-calendar me-1"></i> Uploaded: {{ document.created_at }}</div>
                                            {% if document.file_size %}
                                            <div><i class="fas fa-weight me-1"></i> Size: {{ (document.file_size / 1024)|round(1) }} KB</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="document-actions">
                                            <a href="{{ url_for('student.view_document', document_id=document.id) }}" 
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-eye me-1"></i> View
                                            </a>
                                            <a href="{{ url_for('student.download_document', document_id=document.id) }}" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-download me-1"></i> Download
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="no-documents">
                            <i class="fas fa-file-alt"></i>
                            <h4>No Documents Found</h4>
                            <p>You haven't uploaded any documents yet. Documents will appear here once they are uploaded during the registration process.</p>
                            <a href="{{ url_for('student.dashboard') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i> Document Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Supported Formats:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i> PDF (.pdf)</li>
                                <li><i class="fas fa-check text-success me-2"></i> Images (.jpg, .jpeg, .png)</li>
                                <li><i class="fas fa-check text-success me-2"></i> Word Documents (.doc, .docx)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Important Notes:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> Maximum file size: 5MB</li>
                                <li><i class="fas fa-shield-alt text-info me-2"></i> Documents are securely stored</li>
                                <li><i class="fas fa-eye text-primary me-2"></i> Only you and authorized staff can view your documents</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
{% endblock %}
