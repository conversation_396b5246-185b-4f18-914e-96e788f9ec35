{% extends 'base.html' %}

{% block title %}Student Portal Login{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/styles.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/modern.css', v='1.1') }}">
<style>
    .login-card {
        max-width: 500px;
        margin: 0 auto;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .login-header {
        background-color: #3c78c3;
        color: white;
        padding: 20px;
        text-align: center;
    }

    .login-body {
        padding: 30px;
    }

    .login-footer {
        background-color: #f8f9fa;
        padding: 15px;
        text-align: center;
        border-top: 1px solid #eee;
    }

    .login-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="login-card">
                <div class="login-header">
                    <div class="login-icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <h2>Student Portal Login</h2>
                    <p class="mb-0">Login with your email and password to access your courses and certificates</p>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="px-4 pt-3">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                <div class="login-body">
                    <form method="post" action="{{ url_for('student.login') }}">
                        <div class="mb-4">
                            <label for="email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email" name="email" value="{{ request.form.get('email', '') }}" required>
                            </div>
                            <div class="form-text">Enter your registered email address</div>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Enter your portal account password</div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i> Login
                            </button>
                        </div>
                    </form>
                </div>

                <div class="login-footer">
                    <p class="mb-0">Need help? Contact the administration office</p>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="{{ url_for('main.registration') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Registration Options
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle password visibility
        const toggleButtons = document.querySelectorAll('.toggle-password');
        if (toggleButtons) {
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const input = this.previousElementSibling;
                    const icon = this.querySelector('i');

                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });
        }
    });
</script>
{% endblock %}