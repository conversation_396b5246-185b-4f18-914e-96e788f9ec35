{% extends 'base.html' %}

{% block title %}My Profile - Student Portal{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/styles.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/registration/modern.css', v='1.1') }}">
<style>
    .profile-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .profile-header {
        background-color: #3c78c3;
        color: white;
        padding: 20px;
        position: relative;
    }
    
    .profile-body {
        padding: 30px;
    }
    
    .profile-photo {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        object-fit: cover;
        border: 5px solid white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar-nav {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }
    
    .sidebar-nav .nav-link {
        color: #495057;
        padding: 10px 15px;
        margin-bottom: 5px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }
    
    .sidebar-nav .nav-link:hover {
        background-color: #e9ecef;
        color: #3c78c3;
    }
    
    .sidebar-nav .nav-link.active {
        background-color: #3c78c3;
        color: white;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
    }
    
    .info-value {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 mb-4">
            <div class="sidebar-nav">
                <h5 class="mb-3">Student Portal</h5>
                <div class="nav flex-column">
                    <a href="{{ url_for('student.dashboard') }}" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{{ url_for('student.profile') }}" class="nav-link active">
                        <i class="fas fa-user"></i> My Profile
                    </a>
                    <a href="{{ url_for('student.documents') }}" class="nav-link">
                        <i class="fas fa-file-alt"></i> My Documents
                    </a>
                    <a href="{{ url_for('student.dashboard') }}#courses" class="nav-link">
                        <i class="fas fa-graduation-cap"></i> My Courses
                    </a>
                    <a href="{{ url_for('student.dashboard') }}#certificates" class="nav-link">
                        <i class="fas fa-certificate"></i> My Certificates
                    </a>
                    <a href="{{ url_for('student.logout') }}" class="nav-link text-danger">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Profile Card -->
            <div class="profile-card">
                <div class="profile-header">
                    <div class="text-center">
                        <h2><i class="fas fa-user-circle me-2"></i> My Profile</h2>
                        <p class="mb-0">View and manage your personal information</p>
                    </div>
                </div>
                
                <div class="profile-body">
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            {% if student.passport_photo %}
                            <img src="{{ url_for('static', filename='uploads/passport_photos/' + student.passport_photo) }}" class="profile-photo" alt="Profile Photo">
                            {% else %}
                            <img src="{{ url_for('static', filename='images/placeholder-user.png') }}" class="profile-photo" alt="Profile Photo">
                            {% endif %}
                            <h4 class="mt-3">{{ student.rank }} {{ student.surname }}, {{ student.other_names }}</h4>
                            <p class="text-muted">{{ student.service_number }}</p>
                        </div>
                        
                        <div class="col-md-8">
                            <h5 class="mb-3">Personal Information</h5>
                            
                            <div class="info-row">
                                <span class="info-label">Full Name:</span>
                                <span class="info-value">{{ student.surname }}, {{ student.other_names }}</span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Service Number:</span>
                                <span class="info-value">{{ student.service_number }}</span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Rank:</span>
                                <span class="info-value">{{ student.rank }}</span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Date of Birth:</span>
                                <span class="info-value">{{ student.date_of_birth }}</span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Gender:</span>
                                <span class="info-value">{{ student.gender }}</span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Current Unit:</span>
                                <span class="info-value">{{ student.current_unit }}</span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Date of Commission:</span>
                                <span class="info-value">{{ student.date_of_commission }}</span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Years in Service:</span>
                                <span class="info-value">{{ student.years_in_service }}</span>
                            </div>
                            
                            {% if student.email %}
                            <div class="info-row">
                                <span class="info-label">Email:</span>
                                <span class="info-value">{{ student.email }}</span>
                            </div>
                            {% endif %}
                            
                            {% if student.phone %}
                            <div class="info-row">
                                <span class="info-label">Phone:</span>
                                <span class="info-value">{{ student.phone }}</span>
                            </div>
                            {% endif %}
                            
                            <div class="info-row">
                                <span class="info-label">Last Login:</span>
                                <span class="info-value">{{ student.last_login or 'Never' }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <a href="{{ url_for('student.profile_edit') }}" class="btn btn-primary">
                                    <i class="fas fa-edit me-2"></i> Edit Profile
                                </a>
                                <a href="{{ url_for('student.change_password') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-lock me-2"></i> Change Password
                                </a>
                                <a href="{{ url_for('student.dashboard') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
{% endblock %}
